// Backup Export JavaScript
// init
moment.locale('it');

$(document).ready(function() {
    
    // Inizializza daterangepicker
    $('#dateRange').daterangepicker({
        locale: {
            format: 'DD/MM/YYYY',
            separator: ' - ',
            applyLabel: 'Applica',
            cancelLabel: '<PERSON><PERSON><PERSON>',
            fromLabel: 'Da',
            toLabel: 'A',
            customRangeLabel: 'Personalizzato',
            weekLabel: 'S',
            daysOfWeek: ['Dom', 'Lun', 'Mar', 'Mer', '<PERSON><PERSON>', 'Ven', 'Sab'],
            monthNames: ['<PERSON>nai<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Aprile', 'Ma<PERSON>', '<PERSON><PERSON><PERSON>',
                        'Lu<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Novembre', 'Dicembre'],
            firstDay: 1
        },
        ranges: {
            'Oggi': [moment(), moment()],
            'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Ultimi 7 giorni': [moment().subtract(6, 'days'), moment()],
            'Ultimi 30 giorni': [moment().subtract(29, 'days'), moment()],
            'Questo mese': [moment().startOf('month'), moment().endOf('month')],
            'Mese scorso': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        startDate: moment().subtract(29, 'days'),
        endDate: moment()
    });
    
    // Variabili per il tracking dell'esportazione
    let exportInProgress = false;
    let exportJobId = null;
    let progressInterval = null;
    
    // Leggi i paths dai div nascosti
    const startExportUrl = $('#startExportUrl').text();
    const progressUrlTemplate = $('#progressUrlTemplate').text();
    
    // Gestione submit form
    $('#exportForm').on('submit', function(e) {
        e.preventDefault();
        
        if (exportInProgress) {
            return;
        }
        
        const dateRange = $('#dateRange').val();
        if (!dateRange) {
            showAlert('danger', 'Seleziona un range di date valido.');
            return;
        }
        
        startExport(dateRange);
    });
    
    // Avvia esportazione
    function startExport(dateRange) {
        exportInProgress = true;
        $('#btnExport').prop('disabled', true).html('<i class="icon-spinner2 spinner"></i> Avvio...');
        $('#progressSection').show();
        $('#resultAlert').hide();
        $('#exportLog').show().html('');
        
        // Parsing date range
        const dates = dateRange.split(' - ');
        const startDate = moment(dates[0], 'DD/MM/YYYY').format('YYYY-MM-DD');
        const endDate = moment(dates[1], 'DD/MM/YYYY').format('YYYY-MM-DD');
        
        addLogEntry('info', 'Avvio esportazione per il periodo: ' + dateRange);
        
        // Chiamata AJAX per avviare l'esportazione
        $.ajax({
            url: startExportUrl,
            method: 'POST',
            data: {
                startDate: startDate,
                endDate: endDate
            },
            success: function(response) {
                if (response.success) {
                    exportJobId = response.jobId;
                    addLogEntry('success', 'Esportazione avviata con ID: ' + exportJobId);
                    addLogEntry('info', 'Trovate ' + response.totalProcedures + ' pratiche da esportare');
                    
                    // Avvia monitoraggio progresso
                    startProgressMonitoring();
                } else {
                    handleExportError(response.message || 'Errore durante l\'avvio dell\'esportazione');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = 'Errore di comunicazione: ' + error;
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                handleExportError(errorMessage);
            }
        });
    }
    
    // Avvia monitoraggio progresso
    function startProgressMonitoring() {
        progressInterval = setInterval(function() {
            checkProgress();
        }, 2000); // Aggiorna ogni 2 secondi
    }
    
    // Controlla progresso
    function checkProgress() {
        if (!exportJobId) return;

        var progressUrl = progressUrlTemplate+ "?jobId=" + exportJobId;
        $.ajax({
            url: progressUrl,
            method: 'GET',
            success: function(response) {
                updateProgress(response);
                
                if (response.completed) {
                    finishExport(response);
                }
            },
            error: function(xhr, status, error) {
                addLogEntry('error', 'Errore durante il controllo del progresso: ' + error);
            }
        });
    }
    
    // Aggiorna progress bar
    function updateProgress(data) {
        const percentage = data.total > 0 ? Math.round((data.processed / data.total) * 100) : 0;
        
        $('#progressBar').css('width', percentage + '%').text(percentage + '%');
        $('#progressStats').text(data.processed + ' / ' + data.total + ' pratiche');
        
        // Aggiungi log per le pratiche completate
        if (data.lastProcessed && data.lastProcessed.length > 0) {
            data.lastProcessed.forEach(function(procedure) {
                if (procedure.success) {
                    addLogEntry('success', '✓ ' + procedure.protocol + ' - Backup completato');
                } else {
                    addLogEntry('error', '✗ ' + procedure.protocol + ' - Errore: ' + (procedure.error || 'Errore sconosciuto'));
                }
            });
        }
    }
    
    // Termina esportazione
    function finishExport(data) {
        clearInterval(progressInterval);
        exportInProgress = false;
        $('#btnExport').prop('disabled', false).html('<i class="icon-cloud-upload"></i> Avvia Esportazione');
        
        const successCount = data.successful || 0;
        const errorCount = data.failed || 0;
        const totalCount = data.total || 0;
        
        if (errorCount === 0) {
            showAlert('success', `Esportazione completata con successo! ${successCount} pratiche esportate.`);
            addLogEntry('success', `Esportazione completata: ${successCount}/${totalCount} pratiche esportate con successo`);
        } else {
            showAlert('warning', `Esportazione completata con alcuni errori. ${successCount} successi, ${errorCount} errori.`);
            addLogEntry('error', `Esportazione completata con errori: ${successCount} successi, ${errorCount} errori`);
        }
    }
    
    // Gestisce errori di esportazione
    function handleExportError(message) {
        exportInProgress = false;
        $('#btnExport').prop('disabled', false).html('<i class="icon-cloud-upload"></i> Avvia Esportazione');
        $('#progressSection').hide();
        showAlert('danger', message);
        addLogEntry('error', message);
        
        if (progressInterval) {
            clearInterval(progressInterval);
        }
    }
    
    // Mostra alert
    function showAlert(type, message) {
        const alertClass = type === 'danger' ? 'alert-danger-custom' : 
                          type === 'success' ? 'alert-success-custom' : 
                          type === 'warning' ? 'alert-warning-custom' :
                          'alert-info-custom';
        
        const icon = type === 'danger' ? 'icon-cross2' : 
                    type === 'success' ? 'icon-checkmark3' : 
                    type === 'warning' ? 'icon-warning2' :
                    'icon-info22';
        
        $('#resultAlert').removeClass().addClass('alert alert-custom ' + alertClass)
                         .html('<i class="' + icon + '"></i> ' + message)
                         .show();
    }
    
    // Aggiunge entry al log
    function addLogEntry(type, message) {
        const timestamp = moment().format('HH:mm:ss');
        const logClass = 'log-' + type;
        const entry = '<div class="log-entry ' + logClass + '">[' + timestamp + '] ' + message + '</div>';
        
        $('#exportLog').append(entry);
        $('#exportLog').scrollTop($('#exportLog')[0].scrollHeight);
    }
    
});
