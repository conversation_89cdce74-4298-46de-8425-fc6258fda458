package com.miocontotermico.pojo;

/**
 *
 * <AUTHOR>
 */
public class Firm extends Pojo {

    // contacts
    private String siteEmail;                           // customer care su sito
    private String shopEmail;                           // customer care su shop

    // notifications
    private String shopNotification;                    // notifiche dallo shop all'ufficio ordini: never, always
    private String shopNotificationEmail;               // email dell'ufficio ordini (e-commerce)

    private String b2bNotification;                     // notifiche dal b2b all'ufficio ordini: never, always
    private String b2bNotificationEmail;                // email dell'ufficio ordini (b2b)

    private String customerNotification;                // notifiche dal b.e. all'acquirente: never, always
    private String vendorNotification;                  // notifiche dal b.e. al venditore: never, always

    // api
    private String apikey;                              // apikey

    // dropbox backup configuration
    private String dropboxAppKey;                       // dropbox app key
    private String dropboxAppSecret;                    // dropbox app secret
    private String dropboxAccessToken;                  // dropbox access token
    private String dropboxRefreshToken;                 // dropbox refresh token
    private Boolean dropboxBackupEnabled;               // enable/disable dropbox backup
    private String dropboxBackupFolder;                 // backup folder name (default: Backup_MCT)
    
    public String getSiteEmail() {
        return siteEmail;
    }

    public void setSiteEmail(String siteEmail) {
        this.siteEmail = siteEmail;
    }

    public String getShopEmail() {
        return shopEmail;
    }

    public void setShopEmail(String shopEmail) {
        this.shopEmail = shopEmail;
    }

    public String getShopNotification() {
        return shopNotification;
    }

    public void setShopNotification(String shopNotification) {
        this.shopNotification = shopNotification;
    }

    public String getShopNotificationEmail() {
        return shopNotificationEmail;
    }

    public void setShopNotificationEmail(String shopNotificationEmail) {
        this.shopNotificationEmail = shopNotificationEmail;
    }

    public String getB2bNotification() {
        return b2bNotification;
    }

    public void setB2bNotification(String b2bNotification) {
        this.b2bNotification = b2bNotification;
    }

    public String getB2bNotificationEmail() {
        return b2bNotificationEmail;
    }

    public void setB2bNotificationEmail(String b2bNotificationEmail) {
        this.b2bNotificationEmail = b2bNotificationEmail;
    }

    public String getCustomerNotification() {
        return customerNotification;
    }

    public void setCustomerNotification(String customerNotification) {
        this.customerNotification = customerNotification;
    }

    public String getVendorNotification() {
        return vendorNotification;
    }

    public void setVendorNotification(String vendorNotification) {
        this.vendorNotification = vendorNotification;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

    public String getDropboxAppKey() {
        return dropboxAppKey;
    }

    public void setDropboxAppKey(String dropboxAppKey) {
        this.dropboxAppKey = dropboxAppKey;
    }

    public String getDropboxAppSecret() {
        return dropboxAppSecret;
    }

    public void setDropboxAppSecret(String dropboxAppSecret) {
        this.dropboxAppSecret = dropboxAppSecret;
    }

    public String getDropboxAccessToken() {
        return dropboxAccessToken;
    }

    public void setDropboxAccessToken(String dropboxAccessToken) {
        this.dropboxAccessToken = dropboxAccessToken;
    }

    public String getDropboxRefreshToken() {
        return dropboxRefreshToken;
    }

    public void setDropboxRefreshToken(String dropboxRefreshToken) {
        this.dropboxRefreshToken = dropboxRefreshToken;
    }

    public Boolean getDropboxBackupEnabled() {
        return dropboxBackupEnabled;
    }

    public void setDropboxBackupEnabled(Boolean dropboxBackupEnabled) {
        this.dropboxBackupEnabled = dropboxBackupEnabled;
    }

    public String getDropboxBackupFolder() {
        return dropboxBackupFolder;
    }

    public void setDropboxBackupFolder(String dropboxBackupFolder) {
        this.dropboxBackupFolder = dropboxBackupFolder;
    }

}
