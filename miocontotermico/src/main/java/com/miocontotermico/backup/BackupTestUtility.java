package com.miocontotermico.backup;

import com.miocontotermico.dao.FirmDao;
import com.miocontotermico.dao.ProcedureDao;
import com.miocontotermico.dropbox.DropboxConfig;
import com.miocontotermico.pojo.Firm;
import com.miocontotermico.pojo.Procedure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for testing the backup system
 * 
 * <AUTHOR> Augster
 */
public class BackupTestUtility {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupTestUtility.class);
    
    /**
     * Tests the complete backup system configuration
     */
    public static void testBackupSystem() {
        LOGGER.info("=== BACKUP SYSTEM TEST ===");
        
        try {
            // Test 1: Check firm configuration
            LOGGER.info("1. Testing firm configuration...");
            Firm firm = FirmDao.loadFirm();
            if (firm == null) {
                LOGGER.error("FAIL: No firm configuration found");
                return;
            }

            // Validate configuration using DropboxConfig
            DropboxConfig config = new DropboxConfig(firm);
            DropboxConfig.ValidationResult validation = config.validate();

            LOGGER.info("Configuration validation: {}", validation.getMessage());

            if (validation.hasErrors()) {
                LOGGER.error("FAIL: Configuration validation failed");
                for (String error : validation.getErrors()) {
                    LOGGER.error("  - {}", error);
                }
                return;
            }

            if (validation.hasWarnings()) {
                for (String warning : validation.getWarnings()) {
                    LOGGER.warn("  - {}", warning);
                }
            }

            LOGGER.info("PASS: Firm configuration is valid");
            
            // Test 2: Test Dropbox connection
            LOGGER.info("2. Testing Dropbox connection...");
            BackupMaintenanceService maintenance = BackupMaintenanceService.getInstance();
            boolean connectionOk = maintenance.testDropboxConnection();
            if (connectionOk) {
                LOGGER.info("PASS: Dropbox connection successful");
            } else {
                LOGGER.error("FAIL: Dropbox connection failed");
                return;
            }
            
            // Test 3: Test backup services initialization
            LOGGER.info("3. Testing backup services...");
            BackupService backupService = new BackupService();
            BackupTaskQueue taskQueue = BackupTaskQueue.getInstance();
            BackupMonitor monitor = BackupMonitor.getInstance();
            BackupTrigger trigger = BackupTrigger.getInstance();
            LOGGER.info("PASS: All backup services initialized successfully");
            
            // Test 4: Test backup status service
            LOGGER.info("4. Testing backup status service...");
            BackupStatusService statusService = BackupStatusService.getInstance();
            LOGGER.info("PASS: Backup status service initialized");
            
            // Test 5: Test verification service
            LOGGER.info("5. Testing verification service...");
            BackupVerificationService verificationService = BackupVerificationService.getInstance();
            LOGGER.info("PASS: Verification service initialized");
            
            // Test 6: Show current statistics
            LOGGER.info("6. Current system statistics:");
            BackupMonitor.BackupStats stats = monitor.getStats();
            LOGGER.info("   Backup stats: {}", stats);
            
            BackupTaskQueue.TaskQueueStats queueStats = taskQueue.getStats();
            LOGGER.info("   Queue stats: {}", queueStats);
            
            BackgroundTaskExecutor.ExecutorStats executorStats = BackgroundTaskExecutor.getInstance().getStats();
            LOGGER.info("   Executor stats: {}", executorStats);
            
            LOGGER.info("=== BACKUP SYSTEM TEST COMPLETED SUCCESSFULLY ===");
            
        } catch (Exception e) {
            LOGGER.error("FAIL: Backup system test failed: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Tests backup for a specific procedure
     * @param protocol the procedure protocol to test
     */
    public static void testProcedureBackup(String protocol) {
        LOGGER.info("=== TESTING BACKUP FOR PROCEDURE: {} ===", protocol);
        
        try {
            // Load procedure
            Procedure procedure = ProcedureDao.loadProcedureByProtocol(protocol);
            if (procedure == null) {
                LOGGER.error("FAIL: Procedure not found: {}", protocol);
                return;
            }
            
            LOGGER.info("Loaded procedure: {} (status: {})", procedure.getProtocol(), procedure.getStatus());
            
            // Test if backup should be triggered
            BackupService backupService = new BackupService();
            boolean shouldBackup = backupService.shouldBackupProcedure(procedure);
            LOGGER.info("Should backup: {}", shouldBackup);
            
            if (!shouldBackup) {
                LOGGER.info("Backup not needed for this procedure (likely draft status)");
                return;
            }
            
            // Trigger backup manually
            LOGGER.info("Triggering backup...");
            BackupTrigger trigger = BackupTrigger.getInstance();
            trigger.triggerBackup(procedure);
            
            // Wait a moment for async processing
            Thread.sleep(2000);
            
            // Check backup status
            BackupStatusService statusService = BackupStatusService.getInstance();
            BackupStatusService.BackupStatusInfo statusInfo = statusService.getBackupStatus(protocol);
            if (statusInfo != null) {
                LOGGER.info("Backup status: {}", statusInfo);
            } else {
                LOGGER.warn("No backup status information available");
            }
            
            // Verify backup
            LOGGER.info("Verifying backup...");
            BackupVerificationService verificationService = BackupVerificationService.getInstance();
            BackupVerificationService.VerificationResult result = verificationService.verifyProcedureBackup(procedure);
            LOGGER.info("Verification result: {}", result);

            // Show backup history for this procedure
            LOGGER.info("Backup history for procedure {}:", protocol);
            BackupMaintenanceService maintenanceService = BackupMaintenanceService.getInstance();
            java.util.List<String> timestamps = maintenanceService.listProcedureBackupTimestamps(protocol);
            if (timestamps.isEmpty()) {
                LOGGER.info("  No backup history found");
            } else {
                for (String timestamp : timestamps) {
                    LOGGER.info("  - {}", timestamp);
                }
            }

            LOGGER.info("=== PROCEDURE BACKUP TEST COMPLETED ===");
            
        } catch (Exception e) {
            LOGGER.error("FAIL: Procedure backup test failed: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Performs system maintenance
     */
    public static void performMaintenance() {
        LOGGER.info("=== PERFORMING BACKUP SYSTEM MAINTENANCE ===");
        
        try {
            BackupMaintenanceService maintenance = BackupMaintenanceService.getInstance();
            BackupMaintenanceService.MaintenanceResult result = maintenance.performMaintenance();
            LOGGER.info("Maintenance result: {}", result);
            
        } catch (Exception e) {
            LOGGER.error("FAIL: Maintenance failed: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Shows current backup system status
     */
    public static void showSystemStatus() {
        LOGGER.info("=== BACKUP SYSTEM STATUS ===");
        
        try {
            // Show monitor stats
            BackupMonitor monitor = BackupMonitor.getInstance();
            BackupMonitor.BackupStats stats = monitor.getStats();
            LOGGER.info("Monitor stats: {}", stats);
            
            // Show queue stats
            BackupTaskQueue taskQueue = BackupTaskQueue.getInstance();
            BackupTaskQueue.TaskQueueStats queueStats = taskQueue.getStats();
            LOGGER.info("Queue stats: {}", queueStats);
            
            // Show executor stats
            BackgroundTaskExecutor executor = BackgroundTaskExecutor.getInstance();
            BackgroundTaskExecutor.ExecutorStats executorStats = executor.getStats();
            LOGGER.info("Executor stats: {}", executorStats);
            
            // Test connection
            BackupMaintenanceService maintenance = BackupMaintenanceService.getInstance();
            boolean connectionOk = maintenance.testDropboxConnection();
            LOGGER.info("Dropbox connection: {}", connectionOk ? "OK" : "FAILED");
            
        } catch (Exception e) {
            LOGGER.error("Error showing system status: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Main method for running tests
     */
    public static void main(String[] args) {
        if (args.length == 0) {
            testBackupSystem();
        } else {
            String command = args[0].toLowerCase();
            switch (command) {
                case "test":
                    testBackupSystem();
                    break;
                case "procedure":
                    if (args.length > 1) {
                        testProcedureBackup(args[1]);
                    } else {
                        LOGGER.error("Usage: java BackupTestUtility procedure <protocol>");
                    }
                    break;
                case "maintenance":
                    performMaintenance();
                    break;
                case "status":
                    showSystemStatus();
                    break;
                default:
                    LOGGER.info("Usage: java BackupTestUtility [test|procedure <protocol>|maintenance|status]");
                    break;
            }
        }
    }
}
