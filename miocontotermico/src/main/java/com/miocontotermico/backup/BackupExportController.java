package com.miocontotermico.backup;

import com.google.gson.Gson;
import com.miocontotermico.core.Manager;
import com.miocontotermico.dao.ProcedureDao;
import com.miocontotermico.pojo.Procedure;
import com.miocontotermico.pojo.User;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Controller for handling backup export operations
 * 
 * <AUTHOR> Augster
 */
public class BackupExportController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupExportController.class);
    private static final Gson gson = new Gson();
    
    // Storage per i job di esportazione attivi
    private static final Map<String, ExportJob> activeJobs = new ConcurrentHashMap<>();
    
    /**
     * Avvia un'esportazione massiva di backup
     */
    public static Route startExport = (Request request, Response response) -> {
        response.type("application/json");
        
        try {
            // Verifica autorizzazioni
            String token = Manager.getToken(request);
            User user = Manager.getUser(token, response);
            if (user == null || (!user.getProfileType().equals("admin") && !user.getProfileType().equals("system"))) {
                response.status(403);
                return gson.toJson(Map.of("success", false, "message", "Accesso negato"));
            }
            
            // Parsing parametri
            String startDateStr = request.queryParams("startDate");
            String endDateStr = request.queryParams("endDate");
            
            if (StringUtils.isBlank(startDateStr) || StringUtils.isBlank(endDateStr)) {
                response.status(400);
                return gson.toJson(Map.of("success", false, "message", "Date di inizio e fine sono obbligatorie"));
            }
            
            // Parsing date
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date startDate = dateFormat.parse(startDateStr);
            Date endDate = dateFormat.parse(endDateStr);
            
            // Carica pratiche nel range di date
            List<Procedure> procedures = ProcedureDao.loadProcedureListByDateRangeAndStatus(
                startDate, endDate, null, null, null);
            
            // Filtra solo pratiche non-draft
            List<Procedure> validProcedures = new ArrayList<>();
            for (Procedure procedure : procedures) {
                if (procedure.getStatus() != null && !"draft".equalsIgnoreCase(procedure.getStatus())) {
                    validProcedures.add(procedure);
                }
            }
            
            if (validProcedures.isEmpty()) {
                return gson.toJson(Map.of(
                    "success", false, 
                    "message", "Nessuna pratica trovata nel range di date specificato"
                ));
            }
            
            // Crea job di esportazione
            String jobId = UUID.randomUUID().toString();
            ExportJob job = new ExportJob(jobId, validProcedures, user.getId());
            activeJobs.put(jobId, job);
            
            // Avvia esportazione in background
            BackgroundTaskExecutor.getInstance().submitBackupTask(() -> {
                executeExport(job);
            });
            
            LOGGER.info("Avviata esportazione massiva per {} pratiche (Job ID: {})", validProcedures.size(), jobId);
            
            return gson.toJson(Map.of(
                "success", true,
                "jobId", jobId,
                "totalProcedures", validProcedures.size(),
                "message", "Esportazione avviata con successo"
            ));
            
        } catch (Exception e) {
            LOGGER.error("Errore durante l'avvio dell'esportazione: {}", e.getMessage(), e);
            response.status(500);
            return gson.toJson(Map.of("success", false, "message", "Errore interno: " + e.getMessage()));
        }
    };
    
    /**
     * Controlla il progresso di un'esportazione
     */
    public static Route getProgress = (Request request, Response response) -> {
        response.type("application/json");
        
        try {
            // Verifica autorizzazioni
            String token = Manager.getToken(request);
            User user = Manager.getUser(token, response);
            if (user == null || (!user.getProfileType().equals("admin") && !user.getProfileType().equals("system"))) {
                response.status(403);
                return gson.toJson(Map.of("success", false, "message", "Accesso negato"));
            }
            
            String jobId = request.queryParams("jobId");
            ExportJob job = activeJobs.get(jobId);
            
            if (job == null) {
                response.status(404);
                return gson.toJson(Map.of("success", false, "message", "Job non trovato"));
            }
            
            // Prepara risposta con progresso
            Map<String, Object> progressData = new HashMap<>();
            progressData.put("jobId", jobId);
            progressData.put("total", job.getTotalCount());
            progressData.put("processed", job.getProcessedCount());
            progressData.put("successful", job.getSuccessfulCount());
            progressData.put("failed", job.getFailedCount());
            progressData.put("completed", job.isCompleted());
            progressData.put("lastProcessed", job.getAndClearLastProcessed());
            
            return gson.toJson(progressData);

        } catch (Exception e) {
            LOGGER.error("Errore durante il controllo del progresso: {}", e.getMessage(), e);
            response.status(500);
            return gson.toJson(Map.of("success", false, "message", "Errore interno: " + e.getMessage()));
        }
    };
    
    /**
     * Esegue l'esportazione di backup
     */
    private static void executeExport(ExportJob job) {
        LOGGER.info("Inizio esecuzione esportazione per Job ID: {}", job.getJobId());
        
        BackupTrigger backupTrigger = BackupTrigger.getInstance();
        
        for (Procedure procedure : job.getProcedures()) {
            try {
                // Trigger backup per la procedura
                backupTrigger.triggerBackup(procedure);
                
                // Aggiorna statistiche job
                job.addProcessedProcedure(procedure.getProtocol(), true, null);
                
                LOGGER.debug("Backup completato per procedura: {}", procedure.getProtocol());
                
                // Piccola pausa per non sovraccaricare il sistema
                Thread.sleep(100);
                
            } catch (Exception e) {
                LOGGER.error("Errore durante il backup della procedura {}: {}", procedure.getProtocol(), e.getMessage(), e);
                job.addProcessedProcedure(procedure.getProtocol(), false, e.getMessage());
            }
        }
        
        job.setCompleted(true);
        
        LOGGER.info("Esportazione completata per Job ID: {} - {} successi, {} errori", 
            job.getJobId(), job.getSuccessfulCount(), job.getFailedCount());
        
        // Rimuovi il job dopo 1 ora per liberare memoria
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                activeJobs.remove(job.getJobId());
                LOGGER.debug("Rimosso job completato: {}", job.getJobId());
            }
        }, 60 * 60 * 1000); // 1 ora
    }
    
    /**
     * Classe per rappresentare un job di esportazione
     */
    public static class ExportJob {
        private final String jobId;
        private final List<Procedure> procedures;
        private final ObjectId userId;
        private final AtomicInteger processedCount = new AtomicInteger(0);
        private final AtomicInteger successfulCount = new AtomicInteger(0);
        private final AtomicInteger failedCount = new AtomicInteger(0);
        private final List<ProcessedProcedure> lastProcessed = Collections.synchronizedList(new ArrayList<>());
        private volatile boolean completed = false;
        
        public ExportJob(String jobId, List<Procedure> procedures, ObjectId userId) {
            this.jobId = jobId;
            this.procedures = procedures;
            this.userId = userId;
        }
        
        public void addProcessedProcedure(String protocol, boolean success, String error) {
            processedCount.incrementAndGet();
            
            if (success) {
                successfulCount.incrementAndGet();
            } else {
                failedCount.incrementAndGet();
            }
            
            // Mantieni solo gli ultimi 10 per evitare accumulo di memoria
            synchronized (lastProcessed) {
                lastProcessed.add(new ProcessedProcedure(protocol, success, error));
                if (lastProcessed.size() > 10) {
                    lastProcessed.remove(0);
                }
            }
        }
        
        public List<ProcessedProcedure> getAndClearLastProcessed() {
            synchronized (lastProcessed) {
                List<ProcessedProcedure> result = new ArrayList<>(lastProcessed);
                lastProcessed.clear();
                return result;
            }
        }
        
        // Getters
        public String getJobId() { return jobId; }
        public List<Procedure> getProcedures() { return procedures; }
        public ObjectId getUserId() { return userId; }
        public int getTotalCount() { return procedures.size(); }
        public int getProcessedCount() { return processedCount.get(); }
        public int getSuccessfulCount() { return successfulCount.get(); }
        public int getFailedCount() { return failedCount.get(); }
        public boolean isCompleted() { return completed; }
        public void setCompleted(boolean completed) { this.completed = completed; }
    }
    
    /**
     * Classe per rappresentare una procedura processata
     */
    public static class ProcessedProcedure {
        private final String protocol;
        private final boolean success;
        private final String error;
        
        public ProcessedProcedure(String protocol, boolean success, String error) {
            this.protocol = protocol;
            this.success = success;
            this.error = error;
        }
        
        public String getProtocol() { return protocol; }
        public boolean isSuccess() { return success; }
        public String getError() { return error; }
    }
}
