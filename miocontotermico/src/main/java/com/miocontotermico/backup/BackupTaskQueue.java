package com.miocontotermico.backup;

import com.miocontotermico.pojo.Procedure;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Task queue system for managing backup operations
 * 
 * <AUTHOR> Augster
 */
public class BackupTaskQueue {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupTaskQueue.class);
    
    private static BackupTaskQueue instance;
    
    private final BackgroundTaskExecutor taskExecutor;
    private final RetryableBackupService retryableBackupService;
    private final ConcurrentHashMap<String, Future<BackupResult>> activeTasks;
    private final AtomicInteger taskCounter = new AtomicInteger(0);
    
    private BackupTaskQueue() {
        this.taskExecutor = BackgroundTaskExecutor.getInstance();
        this.retryableBackupService = new RetryableBackupService();
        this.activeTasks = new ConcurrentHashMap<>();

        LOGGER.info("BackupTaskQueue initialized");
    }
    
    /**
     * Gets the singleton instance
     */
    public static synchronized BackupTaskQueue getInstance() {
        if (instance == null) {
            instance = new BackupTaskQueue();
        }
        return instance;
    }
    
    /**
     * Queues a procedure for backup
     * @param procedure the procedure to backup
     * @return true if task was queued, false if already in progress or invalid
     */
    public boolean queueBackup(Procedure procedure) {
        if (procedure == null) {
            LOGGER.error("Cannot queue backup for null procedure");
            return false;
        }
        
        if (StringUtils.isBlank(procedure.getProtocol())) {
            LOGGER.error("Cannot queue backup for procedure without protocol");
            return false;
        }
        
        // Check if backup should be performed
        if (!retryableBackupService.shouldBackupProcedure(procedure)) {
            LOGGER.debug("Backup not needed for procedure {}: status={}, cancelled={}", 
                procedure.getProtocol(), procedure.getStatus(), procedure.getCancelled());
            return false;
        }
        
        String protocol = procedure.getProtocol();
        
        // Check if backup is already in progress for this procedure
        if (isBackupInProgress(protocol)) {
            LOGGER.debug("Backup already in progress for procedure {}", protocol);
            return false;
        }
        
        // Create backup task
        BackupTask backupTask = new BackupTask(procedure, taskCounter.incrementAndGet());
        
        // Submit task for execution
        Future<BackupResult> future = taskExecutor.submitBackupTask(backupTask);
        
        // Track the task
        activeTasks.put(protocol, future);
        
        LOGGER.info("Queued backup task for procedure {}", protocol);
        return true;
    }
    
    /**
     * Checks if a backup is currently in progress for a procedure
     * @param protocol the procedure protocol
     * @return true if backup is in progress
     */
    public boolean isBackupInProgress(String protocol) {
        if (StringUtils.isBlank(protocol)) {
            return false;
        }
        
        Future<BackupResult> future = activeTasks.get(protocol);
        if (future == null) {
            return false;
        }
        
        // Check if task is done
        if (future.isDone()) {
            // Remove completed task
            activeTasks.remove(protocol);
            return false;
        }
        
        return true;
    }
    
    /**
     * Gets the result of a backup task if completed
     * @param protocol the procedure protocol
     * @return backup result if completed, null if not found or still in progress
     */
    public BackupResult getBackupResult(String protocol) {
        if (StringUtils.isBlank(protocol)) {
            return null;
        }
        
        Future<BackupResult> future = activeTasks.get(protocol);
        if (future == null) {
            return null;
        }
        
        if (!future.isDone()) {
            return null;
        }
        
        try {
            BackupResult result = future.get();
            // Remove completed task
            activeTasks.remove(protocol);
            return result;
        } catch (Exception e) {
            LOGGER.error("Error getting backup result for procedure {}: {}", protocol, e.getMessage());
            activeTasks.remove(protocol);
            return BackupResult.failure("Error retrieving result: " + e.getMessage());
        }
    }
    
    /**
     * Cancels a backup task if it's in progress
     * @param protocol the procedure protocol
     * @return true if task was cancelled, false if not found or already completed
     */
    public boolean cancelBackup(String protocol) {
        if (StringUtils.isBlank(protocol)) {
            return false;
        }
        
        Future<BackupResult> future = activeTasks.get(protocol);
        if (future == null) {
            return false;
        }
        
        boolean cancelled = future.cancel(true);
        if (cancelled) {
            activeTasks.remove(protocol);
            LOGGER.info("Cancelled backup task for procedure {}", protocol);
        }
        
        return cancelled;
    }
    
    /**
     * Gets the number of active backup tasks
     */
    public int getActiveTaskCount() {
        // Clean up completed tasks
        activeTasks.entrySet().removeIf(entry -> entry.getValue().isDone());
        return activeTasks.size();
    }
    
    /**
     * Gets task queue statistics
     */
    public TaskQueueStats getStats() {
        // Clean up completed tasks
        activeTasks.entrySet().removeIf(entry -> entry.getValue().isDone());
        
        BackgroundTaskExecutor.ExecutorStats executorStats = taskExecutor.getStats();
        
        return new TaskQueueStats(
            activeTasks.size(),
            taskCounter.get(),
            executorStats
        );
    }
    
    /**
     * Inner class representing a backup task
     */
    private class BackupTask implements java.util.concurrent.Callable<BackupResult> {
        
        private final Procedure procedure;
        private final int taskId;
        
        public BackupTask(Procedure procedure, int taskId) {
            this.procedure = procedure;
            this.taskId = taskId;
        }
        
        @Override
        public BackupResult call() {
            String protocol = procedure.getProtocol();
            
            try {
                LOGGER.info("Starting backup task #{} for procedure {}", taskId, protocol);

                // Record backup start in procedure
                BackupStatusService.getInstance().recordBackupStart(protocol);

                BackupResult result = retryableBackupService.backupProcedureWithRetry(procedure);

                // Update backup status in procedure
                BackupStatusService.getInstance().updateBackupStatus(protocol, result);

                LOGGER.info("Completed backup task #{} for procedure {}: {}",
                    taskId, protocol, result.getStatus());

                return result;
                
            } catch (Exception e) {
                LOGGER.error("Error in backup task #{} for procedure {}: {}", 
                    taskId, protocol, e.getMessage(), e);
                return BackupResult.failure("Task execution error: " + e.getMessage());
            } finally {
                // Ensure task is removed from active tasks
                activeTasks.remove(protocol);
            }
        }
    }
    
    /**
     * Statistics about the task queue
     */
    public static class TaskQueueStats {
        private final int activeBackupTasks;
        private final int totalSubmittedTasks;
        private final BackgroundTaskExecutor.ExecutorStats executorStats;
        
        public TaskQueueStats(int activeBackupTasks, int totalSubmittedTasks, 
                             BackgroundTaskExecutor.ExecutorStats executorStats) {
            this.activeBackupTasks = activeBackupTasks;
            this.totalSubmittedTasks = totalSubmittedTasks;
            this.executorStats = executorStats;
        }
        
        public int getActiveBackupTasks() { return activeBackupTasks; }
        public int getTotalSubmittedTasks() { return totalSubmittedTasks; }
        public BackgroundTaskExecutor.ExecutorStats getExecutorStats() { return executorStats; }
        
        @Override
        public String toString() {
            return String.format("TaskQueueStats{activeBackups=%d, totalSubmitted=%d, executor=%s}",
                activeBackupTasks, totalSubmittedTasks, executorStats);
        }
    }
}
