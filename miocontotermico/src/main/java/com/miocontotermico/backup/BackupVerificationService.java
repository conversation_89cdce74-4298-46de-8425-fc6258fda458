package com.miocontotermico.backup;

import com.dropbox.core.v2.files.ListFolderResult;
import com.dropbox.core.v2.files.Metadata;
import com.miocontotermico.dao.FirmDao;
import com.miocontotermico.dropbox.DropboxConfig;
import com.miocontotermico.dropbox.DropboxService;
import com.miocontotermico.pojo.Firm;
import com.miocontotermico.pojo.Procedure;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Service for verifying backup operations and integrity
 * 
 * <AUTHOR> Augster
 */
public class BackupVerificationService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupVerificationService.class);
    
    private static BackupVerificationService instance;
    
    private final FileExtractionService fileExtractionService;
    
    private BackupVerificationService() {
        this.fileExtractionService = new FileExtractionService();
        LOGGER.info("BackupVerificationService initialized");
    }
    
    /**
     * Gets the singleton instance
     */
    public static synchronized BackupVerificationService getInstance() {
        if (instance == null) {
            instance = new BackupVerificationService();
        }
        return instance;
    }
    
    /**
     * Verifies a procedure backup in Dropbox
     * @param procedure the procedure to verify
     * @return verification result
     */
    public VerificationResult verifyProcedureBackup(Procedure procedure) {
        if (procedure == null) {
            return VerificationResult.failure("Procedure cannot be null");
        }
        
        if (StringUtils.isBlank(procedure.getProtocol())) {
            return VerificationResult.failure("Procedure protocol cannot be blank");
        }
        
        try {
            // Load firm configuration
            Firm firm = FirmDao.loadFirm();
            if (firm == null) {
                return VerificationResult.failure("Firm configuration not found");
            }
            
            // Create Dropbox configuration
            DropboxConfig config = new DropboxConfig(firm);
            if (!config.isBackupEnabled()) {
                return VerificationResult.skipped("Dropbox backup is disabled");
            }
            
            // Create Dropbox service
            DropboxService dropboxService = new DropboxService(config);
            
            // Get procedure backup path (includes timestamp)
            String procedureBackupPath = config.getProcedureBackupPath(procedure.getProtocol());

            // Verify backup folder exists
            if (!dropboxService.folderExists(procedureBackupPath)) {
                return VerificationResult.failure("Backup folder does not exist: " + procedureBackupPath);
            }
            
            // Verify procedure JSON exists
            VerificationResult jsonResult = verifyProcedureJson(dropboxService, procedureBackupPath);
            if (!jsonResult.isSuccess()) {
                return jsonResult;
            }
            
            // Verify procedure files
            VerificationResult filesResult = verifyProcedureFiles(dropboxService, procedure, procedureBackupPath);
            if (!filesResult.isSuccess()) {
                return filesResult;
            }
            
            LOGGER.info("Backup verification successful for procedure {}", procedure.getProtocol());
            return VerificationResult.success("Backup verification completed successfully");
            
        } catch (Exception e) {
            LOGGER.error("Error verifying backup for procedure {}: {}", procedure.getProtocol(), e.getMessage(), e);
            return VerificationResult.failure("Verification error: " + e.getMessage());
        }
    }
    
    /**
     * Verifies the procedure JSON file exists
     */
    private VerificationResult verifyProcedureJson(DropboxService dropboxService, String backupPath) {
        try {
            ListFolderResult folderResult = dropboxService.listFolder(backupPath);
            if (folderResult == null) {
                return VerificationResult.failure("Could not list backup folder contents");
            }
            
            boolean jsonFound = false;
            for (Metadata metadata : folderResult.getEntries()) {
                if ("procedure.json".equals(metadata.getName())) {
                    jsonFound = true;
                    break;
                }
            }
            
            if (!jsonFound) {
                return VerificationResult.failure("procedure.json not found in backup");
            }
            
            return VerificationResult.success("procedure.json verified");
            
        } catch (Exception e) {
            return VerificationResult.failure("Error verifying procedure JSON: " + e.getMessage());
        }
    }
    
    /**
     * Verifies procedure files exist in backup
     */
    private VerificationResult verifyProcedureFiles(DropboxService dropboxService, Procedure procedure, String backupPath) {
        try {
            // Extract expected files from procedure
            List<ProcedureFile> expectedFiles = fileExtractionService.extractAllFiles(procedure);
            
            if (expectedFiles.isEmpty()) {
                LOGGER.debug("No files expected for procedure {}", procedure.getProtocol());
                return VerificationResult.success("No files to verify");
            }
            
            // Get all files in backup folder recursively
            List<String> backupFiles = getAllBackupFiles(dropboxService, backupPath);
            
            int foundFiles = 0;
            int missingFiles = 0;
            List<String> missingFileNames = new ArrayList<>();
            
            for (ProcedureFile expectedFile : expectedFiles) {
                String expectedPath = expectedFile.getCategory() + "/" + expectedFile.getFileName();
                
                boolean found = backupFiles.stream()
                    .anyMatch(backupFile -> backupFile.endsWith(expectedPath) || 
                             backupFile.endsWith(expectedFile.getFileName()));
                
                if (found) {
                    foundFiles++;
                } else {
                    missingFiles++;
                    missingFileNames.add(expectedPath);
                }
            }
            
            if (missingFiles > 0) {
                String message = String.format("Missing %d files out of %d expected. Missing: %s", 
                    missingFiles, expectedFiles.size(), String.join(", ", missingFileNames));
                return VerificationResult.failure(message);
            }
            
            String message = String.format("All %d files verified successfully", foundFiles);
            return VerificationResult.success(message);
            
        } catch (Exception e) {
            return VerificationResult.failure("Error verifying procedure files: " + e.getMessage());
        }
    }
    
    /**
     * Gets all files in backup folder recursively
     */
    private List<String> getAllBackupFiles(DropboxService dropboxService, String folderPath) {
        List<String> allFiles = new ArrayList<>();
        
        try {
            ListFolderResult result = dropboxService.listFolder(folderPath);
            if (result == null) {
                return allFiles;
            }
            
            for (Metadata metadata : result.getEntries()) {
                String relativePath = metadata.getName();
                
                if (metadata instanceof com.dropbox.core.v2.files.FileMetadata) {
                    allFiles.add(relativePath);
                } else if (metadata instanceof com.dropbox.core.v2.files.FolderMetadata) {
                    // Recursively get files from subfolder
                    String subfolderPath = folderPath + "/" + relativePath;
                    List<String> subfolderFiles = getAllBackupFiles(dropboxService, subfolderPath);
                    for (String subfile : subfolderFiles) {
                        allFiles.add(relativePath + "/" + subfile);
                    }
                }
            }
            
        } catch (Exception e) {
            LOGGER.error("Error listing files in folder {}: {}", folderPath, e.getMessage());
        }
        
        return allFiles;
    }
    
    /**
     * Verifies backup integrity for multiple procedures
     * @param procedures list of procedures to verify
     * @return overall verification result
     */
    public BatchVerificationResult verifyMultipleProcedures(List<Procedure> procedures) {
        if (procedures == null || procedures.isEmpty()) {
            return new BatchVerificationResult(0, 0, 0, "No procedures to verify");
        }
        
        int successful = 0;
        int failed = 0;
        int skipped = 0;
        List<String> failedProcedures = new ArrayList<>();
        
        for (Procedure procedure : procedures) {
            try {
                VerificationResult result = verifyProcedureBackup(procedure);
                
                switch (result.getStatus()) {
                    case SUCCESS:
                        successful++;
                        break;
                    case FAILURE:
                        failed++;
                        failedProcedures.add(procedure.getProtocol() + ": " + result.getMessage());
                        break;
                    case SKIPPED:
                        skipped++;
                        break;
                }
                
            } catch (Exception e) {
                failed++;
                failedProcedures.add(procedure.getProtocol() + ": " + e.getMessage());
            }
        }
        
        String message = String.format("Verified %d procedures: %d successful, %d failed, %d skipped", 
            procedures.size(), successful, failed, skipped);
        
        return new BatchVerificationResult(successful, failed, skipped, message, failedProcedures);
    }
    
    /**
     * Result of backup verification
     */
    public static class VerificationResult {
        public enum Status { SUCCESS, FAILURE, SKIPPED }
        
        private final Status status;
        private final String message;
        
        private VerificationResult(Status status, String message) {
            this.status = status;
            this.message = message;
        }
        
        public static VerificationResult success(String message) {
            return new VerificationResult(Status.SUCCESS, message);
        }
        
        public static VerificationResult failure(String message) {
            return new VerificationResult(Status.FAILURE, message);
        }
        
        public static VerificationResult skipped(String message) {
            return new VerificationResult(Status.SKIPPED, message);
        }
        
        public Status getStatus() { return status; }
        public String getMessage() { return message; }
        public boolean isSuccess() { return status == Status.SUCCESS; }
        public boolean isFailure() { return status == Status.FAILURE; }
        public boolean isSkipped() { return status == Status.SKIPPED; }
        
        @Override
        public String toString() {
            return String.format("VerificationResult{status=%s, message='%s'}", status, message);
        }
    }
    
    /**
     * Result of batch verification
     */
    public static class BatchVerificationResult {
        private final int successful;
        private final int failed;
        private final int skipped;
        private final String message;
        private final List<String> failedProcedures;
        
        public BatchVerificationResult(int successful, int failed, int skipped, String message) {
            this(successful, failed, skipped, message, new ArrayList<>());
        }
        
        public BatchVerificationResult(int successful, int failed, int skipped, String message, List<String> failedProcedures) {
            this.successful = successful;
            this.failed = failed;
            this.skipped = skipped;
            this.message = message;
            this.failedProcedures = failedProcedures != null ? failedProcedures : new ArrayList<>();
        }
        
        public int getSuccessful() { return successful; }
        public int getFailed() { return failed; }
        public int getSkipped() { return skipped; }
        public String getMessage() { return message; }
        public List<String> getFailedProcedures() { return failedProcedures; }
        
        public boolean hasFailures() { return failed > 0; }
        
        @Override
        public String toString() {
            return String.format("BatchVerificationResult{successful=%d, failed=%d, skipped=%d, message='%s'}", 
                successful, failed, skipped, message);
        }
    }
}
