package com.miocontotermico.backup;

import com.miocontotermico.dao.FileDao;
import com.miocontotermico.pojo.Procedure;
import com.miocontotermico.support.file.Filex;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Service for extracting files from MongoDB GridFS for backup
 * 
 * <AUTHOR> Augster
 */
public class FileExtractionService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(FileExtractionService.class);
    
    /**
     * Extracts all files from a procedure
     * @param procedure the procedure to extract files from
     * @return list of procedure files
     */
    public List<ProcedureFile> extractAllFiles(Procedure procedure) {
        if (procedure == null) {
            return new ArrayList<>();
        }
        
        List<ProcedureFile> allFiles = new ArrayList<>();
        
        // Extract files from all file ID lists in the procedure
        extractFiles(allFiles, procedure.getThermalPortalDelegationFileIds(), "thermal_portal_delegation");
        extractFiles(allFiles, procedure.getIdentityDocumentFileIds(), "identity_document");
        extractFiles(allFiles, procedure.getTinFileIds(), "tin");
        extractFiles(allFiles, procedure.getAuthorizationFormFileIds(), "authorization_form");
        extractFiles(allFiles, procedure.getIdentityDocumentOwnerFormFileIds(), "identity_document_owner");
        extractFiles(allFiles, procedure.getInvoiceFileIds(), "invoice");
        extractFiles(allFiles, procedure.getContractFileIds(), "contract");
        extractFiles(allFiles, procedure.getDataCollectionFormFileIds(), "data_collection_form");
        extractFiles(allFiles, procedure.getTechnicalSheetFileIds(), "technical_sheet");
        extractFiles(allFiles, procedure.getDisposalDocumentFileIds(), "disposal_document");
        extractFiles(allFiles, procedure.getIdentityDocumentAgentFileIds(), "identity_document_agent");
        extractFiles(allFiles, procedure.getCashingMandateFileIds(), "cashing_mandate");
        
        // Photo files - Group 1
        extractFiles(allFiles, procedure.getPlate1FileIds(), "photos/plate_1");
        extractFiles(allFiles, procedure.getThermalPlant1FileIds(), "photos/thermal_plant_1");
        extractFiles(allFiles, procedure.getGenerator1FileIds(), "photos/generator_1");
        extractFiles(allFiles, procedure.getValves1FileIds(), "photos/valves_1");
        
        // Photo files - Group 2
        extractFiles(allFiles, procedure.getPlate2FileIds(), "photos/plate_2");
        extractFiles(allFiles, procedure.getThermal2PlantFileIds(), "photos/thermal_plant_2");
        extractFiles(allFiles, procedure.getGenerator2FileIds(), "photos/generator_2");
        extractFiles(allFiles, procedure.getValves2FileIds(), "photos/valves_2");
        extractFiles(allFiles, procedure.getGlobalStorage2FileIds(), "photos/global_storage_2");
        
        // Photo files - Group 3
        extractFiles(allFiles, procedure.getDetailPanel3FileIds(), "photos/detail_panel_3");
        extractFiles(allFiles, procedure.getDetailPlate3FileIds(), "photos/detail_plate_3");
        extractFiles(allFiles, procedure.getDetailBoiler3FileIds(), "photos/detail_boiler_3");
        extractFiles(allFiles, procedure.getGlobalInstalling3FieldFileIds(), "photos/global_installing_3");
        extractFiles(allFiles, procedure.getGlobalField3FileIds(), "photos/global_field_3");
        extractFiles(allFiles, procedure.getValves3FileIds(), "photos/valves_3");
        
        // Photo files - Group 4
        extractFiles(allFiles, procedure.getDetailGenerator4FileIds(), "photos/detail_generator_4");
        extractFiles(allFiles, procedure.getGlobalGenerator4FileIds(), "photos/global_generator_4");
        extractFiles(allFiles, procedure.getPlate4FileIds(), "photos/plate_4");
        
        // Photo files - Group 5
        extractFiles(allFiles, procedure.getPlate5FileIds(), "photos/plate_5");
        extractFiles(allFiles, procedure.getGenerator5FileIds(), "photos/generator_5");
        extractFiles(allFiles, procedure.getThermalPlant5FileIds(), "photos/thermal_plant_5");
        extractFiles(allFiles, procedure.getValves5FileIds(), "photos/valves_5");
        
        // Final files
        extractFiles(allFiles, procedure.getFinalConventionFileIds(), "final_convention");
        extractFiles(allFiles, procedure.getFinalConventionToSignedIds(), "final_convention_signed");
        extractFiles(allFiles, procedure.getInternalFileIds(), "internal");
        
        LOGGER.info("Extracted {} files from procedure {}", allFiles.size(), procedure.getProtocol());
        return allFiles;
    }
    
    /**
     * Extracts files from a list of ObjectIds
     */
    private void extractFiles(List<ProcedureFile> allFiles, List<ObjectId> fileIds, String category) {
        if (fileIds == null || fileIds.isEmpty()) {
            return;
        }
        
        for (ObjectId fileId : fileIds) {
            try {
                ProcedureFile procedureFile = extractFile(fileId, category);
                if (procedureFile != null) {
                    allFiles.add(procedureFile);
                }
            } catch (Exception e) {
                LOGGER.error("Error extracting file {} from category {}: {}", fileId, category, e.getMessage());
            }
        }
    }
    
    /**
     * Extracts a single file from GridFS
     */
    private ProcedureFile extractFile(ObjectId fileId, String category) {
        if (fileId == null) {
            return null;
        }
        
        try {
            Filex file = FileDao.loadFile(fileId);
            if (file == null) {
                LOGGER.warn("File not found in GridFS: {}", fileId);
                return null;
            }
            
            String fileName = file.getFilename();
            String originalFileName = file.getOriginalFilename();
            byte[] content = file.getBytes();
            String contentType = file.getContentType();
            
            // Use original filename if available, otherwise use GridFS filename
            String displayName = StringUtils.isNotBlank(originalFileName) ? originalFileName : fileName;
            
            return new ProcedureFile(fileName, displayName, category, content, contentType);
            
        } catch (Exception e) {
            LOGGER.error("Error loading file {} from GridFS: {}", fileId, e.getMessage());
            return null;
        }
    }
}
