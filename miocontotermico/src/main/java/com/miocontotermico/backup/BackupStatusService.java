package com.miocontotermico.backup;

import com.miocontotermico.core.Manager;
import com.miocontotermico.dao.ProcedureDao;
import com.miocontotermico.pojo.Procedure;
import com.mongodb.client.MongoCollection;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * Service for updating backup status in Procedure entities
 * 
 * <AUTHOR> Augster
 */
public class BackupStatusService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupStatusService.class);
    
    private static BackupStatusService instance;
    
    private BackupStatusService() {
        LOGGER.info("BackupStatusService initialized");
    }
    
    /**
     * Gets the singleton instance
     */
    public static synchronized BackupStatusService getInstance() {
        if (instance == null) {
            instance = new BackupStatusService();
        }
        return instance;
    }
    
    /**
     * Updates backup status for a procedure
     * @param protocol the procedure protocol
     * @param result the backup result
     */
    public void updateBackupStatus(String protocol, BackupResult result) {
        if (StringUtils.isBlank(protocol)) {
            LOGGER.error("Cannot update backup status for blank protocol");
            return;
        }
        
        if (result == null) {
            LOGGER.error("Cannot update backup status for null result");
            return;
        }
        
        try {
            // Load procedure by protocol
            Procedure procedure = ProcedureDao.loadProcedureByProtocol(protocol);
            if (procedure == null) {
                LOGGER.warn("Procedure not found for protocol: {}", protocol);
                return;
            }
            
            // Update backup status fields
            Date now = new Date();
            procedure.setLastBackupAttempt(now);
            procedure.setLastBackupStatus(result.getStatus().toString());
            procedure.setLastBackupMessage(result.getMessage());
            
            if (result.isSuccess()) {
                procedure.setLastSuccessfulBackup(now);
            }
            
            // Save procedure directly to avoid triggering backup loop
            // We need to update the procedure without triggering the backup trigger
            updateProcedureDirectly(procedure);
            
            LOGGER.debug("Updated backup status for procedure {}: {}", protocol, result.getStatus());
            
        } catch (Exception e) {
            LOGGER.error("Error updating backup status for procedure {}: {}", protocol, e.getMessage(), e);
        }
    }
    
    /**
     * Records backup attempt start
     * @param protocol the procedure protocol
     */
    public void recordBackupStart(String protocol) {
        if (StringUtils.isBlank(protocol)) {
            return;
        }
        
        try {
            Procedure procedure = ProcedureDao.loadProcedureByProtocol(protocol);
            if (procedure == null) {
                return;
            }
            
            procedure.setLastBackupAttempt(new Date());
            procedure.setLastBackupStatus("IN_PROGRESS");
            procedure.setLastBackupMessage("Backup in progress...");
            
            updateProcedureDirectly(procedure);
            
        } catch (Exception e) {
            LOGGER.error("Error recording backup start for procedure {}: {}", protocol, e.getMessage());
        }
    }
    
    /**
     * Gets backup status for a procedure
     * @param protocol the procedure protocol
     * @return backup status info or null if not found
     */
    public BackupStatusInfo getBackupStatus(String protocol) {
        if (StringUtils.isBlank(protocol)) {
            return null;
        }
        
        try {
            Procedure procedure = ProcedureDao.loadProcedureByProtocol(protocol);
            if (procedure == null) {
                return null;
            }
            
            return new BackupStatusInfo(
                procedure.getLastBackupAttempt(),
                procedure.getLastBackupStatus(),
                procedure.getLastBackupMessage(),
                procedure.getLastSuccessfulBackup()
            );
            
        } catch (Exception e) {
            LOGGER.error("Error getting backup status for procedure {}: {}", protocol, e.getMessage());
            return null;
        }
    }

    /**
     * Updates procedure directly in database without triggering backup
     * @param procedure the procedure to update
     */
    private void updateProcedureDirectly(Procedure procedure) throws Exception {
        if (procedure == null || procedure.getId() == null) {
            return;
        }

        // Update last update timestamp
        procedure.setLastUpdate(new Date());

        // Update directly in MongoDB without going through ProcedureDao.updateProcedure
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        collection.replaceOne(
            new Document("_id", procedure.getId()),
            Manager.toDocument(procedure)
        );
    }

    /**
     * Backup status information
     */
    public static class BackupStatusInfo {
        private final Date lastBackupAttempt;
        private final String lastBackupStatus;
        private final String lastBackupMessage;
        private final Date lastSuccessfulBackup;
        
        public BackupStatusInfo(Date lastBackupAttempt, String lastBackupStatus, 
                               String lastBackupMessage, Date lastSuccessfulBackup) {
            this.lastBackupAttempt = lastBackupAttempt;
            this.lastBackupStatus = lastBackupStatus;
            this.lastBackupMessage = lastBackupMessage;
            this.lastSuccessfulBackup = lastSuccessfulBackup;
        }
        
        public Date getLastBackupAttempt() { return lastBackupAttempt; }
        public String getLastBackupStatus() { return lastBackupStatus; }
        public String getLastBackupMessage() { return lastBackupMessage; }
        public Date getLastSuccessfulBackup() { return lastSuccessfulBackup; }
        
        public boolean hasBackupAttempt() {
            return lastBackupAttempt != null;
        }
        
        public boolean isLastBackupSuccessful() {
            return "SUCCESS".equals(lastBackupStatus);
        }
        
        @Override
        public String toString() {
            return String.format("BackupStatusInfo{status='%s', lastAttempt=%s, lastSuccess=%s}",
                lastBackupStatus, lastBackupAttempt, lastSuccessfulBackup);
        }
    }
}
