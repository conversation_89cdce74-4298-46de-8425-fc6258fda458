package com.miocontotermico.backup;

import com.miocontotermico.pojo.Procedure;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Handles triggering of backup operations when procedures are updated
 * 
 * <AUTHOR> Augster
 */
public class BackupTrigger {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupTrigger.class);
    
    private static BackupTrigger instance;
    
    private final BackupTaskQueue backupTaskQueue;
    private final BackupMonitor backupMonitor;
    private final RetryableBackupService retryableBackupService;
    
    private BackupTrigger() {
        this.backupTaskQueue = BackupTaskQueue.getInstance();
        this.backupMonitor = BackupMonitor.getInstance();
        this.retryableBackupService = new RetryableBackupService();
        
        LOGGER.info("BackupTrigger initialized");
    }
    
    /**
     * Gets the singleton instance
     */
    public static synchronized BackupTrigger getInstance() {
        if (instance == null) {
            instance = new BackupTrigger();
        }
        return instance;
    }
    
    /**
     * Triggers backup for a procedure after it has been updated
     * @param procedure the updated procedure
     */
    public void triggerBackup(Procedure procedure) {
        if (procedure == null) {
            LOGGER.debug("Cannot trigger backup for null procedure");
            return;
        }
        
        if (StringUtils.isBlank(procedure.getProtocol())) {
            LOGGER.debug("Cannot trigger backup for procedure without protocol");
            return;
        }
        
        String protocol = procedure.getProtocol();
        
        try {
            // Check if backup should be performed
            if (!retryableBackupService.shouldBackupProcedure(procedure)) {
                LOGGER.debug("Backup not needed for procedure {}: status={}, cancelled={}", 
                    protocol, procedure.getStatus(), procedure.getCancelled());
                return;
            }
            
            // Record backup start for monitoring
            backupMonitor.recordBackupStart(protocol);
            
            // Queue backup task
            boolean queued = backupTaskQueue.queueBackup(procedure);
            
            if (queued) {
                LOGGER.info("Triggered backup for procedure {}", protocol);
            } else {
                LOGGER.debug("Backup not queued for procedure {} (already in progress or invalid)", protocol);
            }
            
        } catch (Exception e) {
            LOGGER.error("Error triggering backup for procedure {}: {}", protocol, e.getMessage(), e);
        }
    }
    
    /**
     * Triggers backup with status change detection
     * @param oldProcedure the procedure before update (can be null for new procedures)
     * @param newProcedure the procedure after update
     */
    public void triggerBackupWithStatusCheck(Procedure oldProcedure, Procedure newProcedure) {
        if (newProcedure == null) {
            LOGGER.debug("Cannot trigger backup for null new procedure");
            return;
        }
        
        String protocol = newProcedure.getProtocol();
        
        // If old procedure is null, this is a new procedure
        if (oldProcedure == null) {
            LOGGER.debug("New procedure created: {}", protocol);
            triggerBackup(newProcedure);
            return;
        }
        
        // Check if status changed from draft to non-draft
        String oldStatus = oldProcedure.getStatus();
        String newStatus = newProcedure.getStatus();
        
        boolean wasStatusChange = !StringUtils.equals(oldStatus, newStatus);
        boolean wasDraft = "draft".equalsIgnoreCase(oldStatus);
        boolean isNowNonDraft = !StringUtils.isBlank(newStatus) && !"draft".equalsIgnoreCase(newStatus);
        
        if (wasStatusChange && wasDraft && isNowNonDraft) {
            LOGGER.info("Procedure {} status changed from draft to {}, triggering backup", protocol, newStatus);
            triggerBackup(newProcedure);
            return;
        }
        
        // Check if procedure was modified (not draft)
        if (!"draft".equalsIgnoreCase(newStatus)) {
            LOGGER.debug("Non-draft procedure {} was updated, triggering backup", protocol);
            triggerBackup(newProcedure);
        } else {
            LOGGER.debug("Draft procedure {} was updated, backup not triggered", protocol);
        }
    }
    
    /**
     * Gets backup status for a procedure
     * @param protocol the procedure protocol
     * @return backup result if available, null otherwise
     */
    public BackupResult getBackupStatus(String protocol) {
        if (StringUtils.isBlank(protocol)) {
            return null;
        }
        
        return backupTaskQueue.getBackupResult(protocol);
    }
    
    /**
     * Checks if backup is in progress for a procedure
     * @param protocol the procedure protocol
     * @return true if backup is in progress
     */
    public boolean isBackupInProgress(String protocol) {
        if (StringUtils.isBlank(protocol)) {
            return false;
        }
        
        return backupTaskQueue.isBackupInProgress(protocol);
    }
    
    /**
     * Gets backup statistics
     */
    public BackupMonitor.BackupStats getBackupStats() {
        return backupMonitor.getStats();
    }
    
    /**
     * Logs current backup statistics
     */
    public void logBackupStats() {
        backupMonitor.logStats();
    }
}
