package com.miocontotermico.backup;

/**
 * Represents the result of a backup operation
 * 
 * <AUTHOR> Augster
 */
public class BackupResult {
    
    public enum Status {
        SUCCESS,
        FAILURE,
        SKIPPED
    }
    
    private final Status status;
    private final String message;
    
    private BackupResult(Status status, String message) {
        this.status = status;
        this.message = message;
    }
    
    public static BackupResult success() {
        return new BackupResult(Status.SUCCESS, "Backup completed successfully");
    }
    
    public static BackupResult success(String message) {
        return new BackupResult(Status.SUCCESS, message);
    }
    
    public static BackupResult failure(String message) {
        return new BackupResult(Status.FAILURE, message);
    }
    
    public static BackupResult skipped(String message) {
        return new BackupResult(Status.SKIPPED, message);
    }
    
    public Status getStatus() {
        return status;
    }
    
    public String getMessage() {
        return message;
    }
    
    public boolean isSuccess() {
        return status == Status.SUCCESS;
    }
    
    public boolean isFailure() {
        return status == Status.FAILURE;
    }
    
    public boolean isSkipped() {
        return status == Status.SKIPPED;
    }
    
    @Override
    public String toString() {
        return "BackupResult{" +
                "status=" + status +
                ", message='" + message + '\'' +
                '}';
    }
}
