package com.miocontotermico.backup;

import com.miocontotermico.dao.FirmDao;
import com.miocontotermico.dropbox.DropboxConfig;
import com.miocontotermico.dropbox.DropboxService;
import com.miocontotermico.pojo.Firm;
import com.miocontotermico.pojo.Procedure;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Main backup service that orchestrates the backup process
 * 
 * <AUTHOR> Augster
 */
public class BackupService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupService.class);
    
    private final FileExtractionService fileExtractionService;
    private final JsonSerializationService jsonSerializationService;
    
    public BackupService() {
        this.fileExtractionService = new FileExtractionService();
        this.jsonSerializationService = new JsonSerializationService();
    }
    
    /**
     * Performs backup of a procedure to Dropbox
     * @param procedure the procedure to backup
     * @return backup result
     */
    public BackupResult backupProcedure(Procedure procedure) {
        if (procedure == null) {
            return BackupResult.failure("Procedure cannot be null");
        }
        
        if (StringUtils.isBlank(procedure.getProtocol())) {
            return BackupResult.failure("Procedure protocol cannot be blank");
        }
        
        try {
            // Load firm configuration
            Firm firm = FirmDao.loadFirm();
            if (firm == null) {
                return BackupResult.failure("Firm configuration not found");
            }
            
            // Create Dropbox configuration
            DropboxConfig config = new DropboxConfig(firm);
            if (!config.isBackupEnabled()) {
                String error = config.getValidationError();
                LOGGER.debug("Backup skipped for procedure {}: {}", procedure.getProtocol(), error);
                return BackupResult.skipped(error);
            }
            
            // Create Dropbox service
            DropboxService dropboxService = new DropboxService(config);
            
            // Test connection
            if (!dropboxService.testConnection()) {
                return BackupResult.failure("Dropbox connection test failed");
            }
            
            // Get procedure backup path (now includes timestamp subfolder)
            String procedureBackupPath = config.getProcedureBackupPath(procedure.getProtocol());

            // Create backup folder (no need to delete since each backup has unique timestamp)
            if (!dropboxService.createFolder(procedureBackupPath)) {
                return BackupResult.failure("Failed to create backup folder: " + procedureBackupPath);
            }
            
            // Backup procedure JSON
            if (!backupProcedureJson(dropboxService, procedure, procedureBackupPath)) {
                return BackupResult.failure("Failed to backup procedure JSON");
            }
            
            // Backup procedure files
            if (!backupProcedureFiles(dropboxService, procedure, procedureBackupPath)) {
                return BackupResult.failure("Failed to backup procedure files");
            }
            
            LOGGER.info("Successfully backed up procedure {} to Dropbox", procedure.getProtocol());
            return BackupResult.success();
            
        } catch (Exception e) {
            LOGGER.error("Error backing up procedure {}: {}", procedure.getProtocol(), e.getMessage(), e);
            return BackupResult.failure("Backup error: " + e.getMessage());
        }
    }
    
    /**
     * Backs up the procedure JSON data
     */
    private boolean backupProcedureJson(DropboxService dropboxService, Procedure procedure, String backupPath) {
        try {
            // Serialize procedure to JSON
            byte[] jsonData = jsonSerializationService.serializeProcedure(procedure);
            if (jsonData == null || jsonData.length == 0) {
                LOGGER.error("Failed to serialize procedure {} to JSON", procedure.getProtocol());
                return false;
            }
            
            // Upload JSON file
            String jsonFilePath = backupPath + "/procedure.json";
            return dropboxService.uploadFile(jsonFilePath, jsonData, "procedure.json");
            
        } catch (Exception e) {
            LOGGER.error("Error backing up procedure JSON for {}: {}", procedure.getProtocol(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Backs up all procedure files
     */
    private boolean backupProcedureFiles(DropboxService dropboxService, Procedure procedure, String backupPath) {
        try {
            // Extract all file IDs from procedure
            List<ProcedureFile> procedureFiles = fileExtractionService.extractAllFiles(procedure);
            
            if (procedureFiles.isEmpty()) {
                LOGGER.info("No files to backup for procedure {}", procedure.getProtocol());
                return true;
            }
            
            LOGGER.info("Backing up {} files for procedure {}", procedureFiles.size(), procedure.getProtocol());
            
            int successCount = 0;
            int failureCount = 0;
            
            for (ProcedureFile procedureFile : procedureFiles) {
                String filePath = backupPath + "/" + procedureFile.getCategory() + "/" + procedureFile.getFileName();
                
                if (dropboxService.uploadFile(filePath, procedureFile.getContent(), procedureFile.getOriginalFileName())) {
                    successCount++;
                } else {
                    failureCount++;
                    LOGGER.error("Failed to backup file: {} (original: {})", 
                        procedureFile.getFileName(), procedureFile.getOriginalFileName());
                }
            }
            
            LOGGER.info("File backup completed for procedure {}: {} successful, {} failed", 
                procedure.getProtocol(), successCount, failureCount);
            
            // Consider backup successful if at least some files were uploaded
            return failureCount == 0 || successCount > 0;
            
        } catch (Exception e) {
            LOGGER.error("Error backing up procedure files for {}: {}", procedure.getProtocol(), e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Checks if a procedure should be backed up based on its status
     * @param procedure the procedure to check
     * @return true if backup should be performed
     */
    public boolean shouldBackupProcedure(Procedure procedure) {
        if (procedure == null) {
            return false;
        }
        
        // Don't backup draft procedures
        if ("draft".equalsIgnoreCase(procedure.getStatus())) {
            return false;
        }
        
        // Don't backup cancelled procedures
        if (procedure.getCancelled() != null && procedure.getCancelled()) {
            return false;
        }
        
        // Must have a protocol
        if (StringUtils.isBlank(procedure.getProtocol())) {
            return false;
        }
        
        return true;
    }
}
