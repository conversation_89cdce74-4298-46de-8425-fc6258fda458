package com.miocontotermico.backup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Background task executor for async backup operations
 * 
 * <AUTHOR> Augster
 */
public class BackgroundTaskExecutor {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackgroundTaskExecutor.class);
    
    private static final int CORE_POOL_SIZE = 2;
    private static final int MAXIMUM_POOL_SIZE = 4;
    private static final long KEEP_ALIVE_TIME = 60L;
    private static final int QUEUE_CAPACITY = 100;
    
    private static BackgroundTaskExecutor instance;
    private final ExecutorService executorService;
    private final AtomicInteger taskCounter = new AtomicInteger(0);
    
    private BackgroundTaskExecutor() {
        // Create thread pool with custom thread factory
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "backup-task-" + threadNumber.getAndIncrement());
                thread.setDaemon(true); // Daemon threads won't prevent JVM shutdown
                thread.setPriority(Thread.NORM_PRIORITY - 1); // Lower priority for background tasks
                return thread;
            }
        };
        
        // Create thread pool executor
        this.executorService = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAXIMUM_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
            threadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy() // Run in caller thread if queue is full
        );
        
        LOGGER.info("BackgroundTaskExecutor initialized with core pool size: {}, max pool size: {}", 
            CORE_POOL_SIZE, MAXIMUM_POOL_SIZE);
    }
    
    /**
     * Gets the singleton instance
     */
    public static synchronized BackgroundTaskExecutor getInstance() {
        if (instance == null) {
            instance = new BackgroundTaskExecutor();
        }
        return instance;
    }
    
    /**
     * Submits a backup task for async execution
     * @param task the backup task to execute
     * @return future representing the task execution
     */
    public Future<BackupResult> submitBackupTask(Callable<BackupResult> task) {
        if (task == null) {
            throw new IllegalArgumentException("Task cannot be null");
        }
        
        int taskId = taskCounter.incrementAndGet();
        LOGGER.debug("Submitting backup task #{}", taskId);
        
        return executorService.submit(() -> {
            try {
                LOGGER.debug("Starting backup task #{}", taskId);
                BackupResult result = task.call();
                LOGGER.debug("Completed backup task #{}: {}", taskId, result.getStatus());
                return result;
                
            } catch (Exception e) {
                LOGGER.error("Error in backup task #{}: {}", taskId, e.getMessage(), e);
                return BackupResult.failure("Task execution error: " + e.getMessage());
            }
        });
    }
    
    /**
     * Submits a backup task for async execution without return value
     * @param task the backup task to execute
     */
    public void submitBackupTask(Runnable task) {
        if (task == null) {
            throw new IllegalArgumentException("Task cannot be null");
        }
        
        int taskId = taskCounter.incrementAndGet();
        LOGGER.debug("Submitting backup task #{} (no return)", taskId);
        
        executorService.submit(() -> {
            try {
                LOGGER.debug("Starting backup task #{} (no return)", taskId);
                task.run();
                LOGGER.debug("Completed backup task #{} (no return)", taskId);
                
            } catch (Exception e) {
                LOGGER.error("Error in backup task #{}: {}", taskId, e.getMessage(), e);
            }
        });
    }
    
    /**
     * Gets executor service statistics
     */
    public ExecutorStats getStats() {
        if (executorService instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) executorService;
            return new ExecutorStats(
                tpe.getActiveCount(),
                tpe.getPoolSize(),
                tpe.getQueue().size(),
                tpe.getCompletedTaskCount(),
                taskCounter.get()
            );
        }
        return new ExecutorStats(0, 0, 0, 0, taskCounter.get());
    }
    
    /**
     * Shuts down the executor service gracefully
     */
    public void shutdown() {
        LOGGER.info("Shutting down BackgroundTaskExecutor...");
        
        executorService.shutdown();
        
        try {
            // Wait for existing tasks to complete
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                LOGGER.warn("Executor did not terminate gracefully, forcing shutdown...");
                executorService.shutdownNow();
                
                // Wait a bit more for tasks to respond to being cancelled
                if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    LOGGER.error("Executor did not terminate after forced shutdown");
                }
            }
        } catch (InterruptedException e) {
            LOGGER.warn("Interrupted while waiting for executor shutdown");
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        LOGGER.info("BackgroundTaskExecutor shutdown completed");
    }
    
    /**
     * Statistics about the executor service
     */
    public static class ExecutorStats {
        private final int activeThreads;
        private final int poolSize;
        private final int queueSize;
        private final long completedTasks;
        private final int totalSubmittedTasks;
        
        public ExecutorStats(int activeThreads, int poolSize, int queueSize, 
                           long completedTasks, int totalSubmittedTasks) {
            this.activeThreads = activeThreads;
            this.poolSize = poolSize;
            this.queueSize = queueSize;
            this.completedTasks = completedTasks;
            this.totalSubmittedTasks = totalSubmittedTasks;
        }
        
        public int getActiveThreads() { return activeThreads; }
        public int getPoolSize() { return poolSize; }
        public int getQueueSize() { return queueSize; }
        public long getCompletedTasks() { return completedTasks; }
        public int getTotalSubmittedTasks() { return totalSubmittedTasks; }
        
        @Override
        public String toString() {
            return String.format("ExecutorStats{active=%d, pool=%d, queue=%d, completed=%d, submitted=%d}",
                activeThreads, poolSize, queueSize, completedTasks, totalSubmittedTasks);
        }
    }
}
