package com.miocontotermico.backup;

/**
 * Represents a file extracted from a procedure for backup
 * 
 * <AUTHOR> Augster
 */
public class ProcedureFile {
    
    private final String fileName;
    private final String originalFileName;
    private final String category;
    private final byte[] content;
    private final String contentType;
    
    public ProcedureFile(String fileName, String originalFileName, String category, 
                        byte[] content, String contentType) {
        this.fileName = fileName;
        this.originalFileName = originalFileName;
        this.category = category;
        this.content = content;
        this.contentType = contentType;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public String getOriginalFileName() {
        return originalFileName;
    }
    
    public String getCategory() {
        return category;
    }
    
    public byte[] getContent() {
        return content;
    }
    
    public String getContentType() {
        return contentType;
    }
}
