package com.miocontotermico.backup;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.User;
import com.miocontotermico.util.RouteUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.TemplateViewRoute;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for the backup export page
 * 
 * <AUTHOR> Augster
 */
public class BackupExportPageController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupExportPageController.class);
    
    /**
     * Renders the backup export page
     */
    public static TemplateViewRoute backupExport = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + "/login");
            return Manager.renderEmpty();
        }
        
        // Check if user is admin or system
        if (!user.getProfileType().equals("admin") && !user.getProfileType().equals("system")) {
            response.status(403);
            attributes.put("error", "Accesso negato. Solo gli amministratori possono accedere a questa pagina.");
            return Manager.render("be/error.html", attributes, RouteUtils.pathType(request));
        }
        
        attributes.put("user", user);
        
        LOGGER.debug("Rendering backup export page for user: {}", user.getFullname());
        
        return Manager.render("be/backup-export.html", attributes, RouteUtils.pathType(request));
    };
}
