package com.miocontotermico.backup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Monitoring and statistics for backup operations
 * 
 * <AUTHOR> Augster
 */
public class BackupMonitor {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupMonitor.class);
    
    private static BackupMonitor instance;
    
    // Counters
    private final AtomicInteger totalBackupsAttempted = new AtomicInteger(0);
    private final AtomicInteger totalBackupsSuccessful = new AtomicInteger(0);
    private final AtomicInteger totalBackupsFailed = new AtomicInteger(0);
    private final AtomicInteger totalBackupsSkipped = new AtomicInteger(0);
    
    // Timing
    private final AtomicLong totalBackupTimeMs = new AtomicLong(0);
    private final AtomicLong lastBackupTimeMs = new AtomicLong(0);
    
    // Recent activity tracking
    private final ConcurrentHashMap<String, BackupActivity> recentActivity = new ConcurrentHashMap<>();
    private static final int MAX_RECENT_ACTIVITIES = 100;
    
    private BackupMonitor() {
        LOGGER.info("BackupMonitor initialized");
    }
    
    /**
     * Gets the singleton instance
     */
    public static synchronized BackupMonitor getInstance() {
        if (instance == null) {
            instance = new BackupMonitor();
        }
        return instance;
    }
    
    /**
     * Records the start of a backup operation
     */
    public void recordBackupStart(String protocol) {
        totalBackupsAttempted.incrementAndGet();
        
        BackupActivity activity = new BackupActivity(protocol, System.currentTimeMillis());
        recentActivity.put(protocol, activity);
        
        // Clean up old activities if we have too many
        if (recentActivity.size() > MAX_RECENT_ACTIVITIES) {
            cleanupOldActivities();
        }
        
        LOGGER.debug("Recorded backup start for procedure {}", protocol);
    }
    
    /**
     * Records the completion of a backup operation
     */
    public void recordBackupComplete(String protocol, BackupResult result) {
        long endTime = System.currentTimeMillis();
        lastBackupTimeMs.set(endTime);
        
        BackupActivity activity = recentActivity.get(protocol);
        if (activity != null) {
            activity.setEndTime(endTime);
            activity.setResult(result);
            
            long duration = endTime - activity.getStartTime();
            totalBackupTimeMs.addAndGet(duration);
        }
        
        // Update counters based on result
        switch (result.getStatus()) {
            case SUCCESS:
                totalBackupsSuccessful.incrementAndGet();
                LOGGER.info("Backup completed successfully for procedure {} in {}ms", 
                    protocol, activity != null ? (endTime - activity.getStartTime()) : "unknown");
                break;
            case FAILURE:
                totalBackupsFailed.incrementAndGet();
                LOGGER.warn("Backup failed for procedure {}: {}", protocol, result.getMessage());
                break;
            case SKIPPED:
                totalBackupsSkipped.incrementAndGet();
                LOGGER.debug("Backup skipped for procedure {}: {}", protocol, result.getMessage());
                break;
        }
    }
    
    /**
     * Gets comprehensive backup statistics
     */
    public BackupStats getStats() {
        int attempted = totalBackupsAttempted.get();
        int successful = totalBackupsSuccessful.get();
        int failed = totalBackupsFailed.get();
        int skipped = totalBackupsSkipped.get();
        
        long totalTime = totalBackupTimeMs.get();
        long avgTime = successful > 0 ? totalTime / successful : 0;
        
        double successRate = attempted > 0 ? (double) successful / attempted * 100 : 0;
        
        return new BackupStats(
            attempted,
            successful,
            failed,
            skipped,
            successRate,
            avgTime,
            lastBackupTimeMs.get(),
            recentActivity.size()
        );
    }
    
    /**
     * Gets recent backup activity
     */
    public ConcurrentHashMap<String, BackupActivity> getRecentActivity() {
        return new ConcurrentHashMap<>(recentActivity);
    }
    
    /**
     * Cleans up old activities to prevent memory leaks
     */
    private void cleanupOldActivities() {
        long cutoffTime = System.currentTimeMillis() - (24 * 60 * 60 * 1000); // 24 hours ago
        
        recentActivity.entrySet().removeIf(entry -> {
            BackupActivity activity = entry.getValue();
            return activity.getStartTime() < cutoffTime;
        });
        
        LOGGER.debug("Cleaned up old backup activities, remaining: {}", recentActivity.size());
    }
    
    /**
     * Logs current statistics
     */
    public void logStats() {
        BackupStats stats = getStats();
        LOGGER.info("Backup Statistics: {}", stats);
        
        // Log task queue stats if available
        try {
            BackupTaskQueue.TaskQueueStats queueStats = BackupTaskQueue.getInstance().getStats();
            LOGGER.info("Task Queue Statistics: {}", queueStats);
        } catch (Exception e) {
            LOGGER.debug("Could not get task queue stats: {}", e.getMessage());
        }
    }
    
    /**
     * Represents backup activity for a specific procedure
     */
    public static class BackupActivity {
        private final String protocol;
        private final long startTime;
        private long endTime;
        private BackupResult result;
        
        public BackupActivity(String protocol, long startTime) {
            this.protocol = protocol;
            this.startTime = startTime;
        }
        
        public String getProtocol() { return protocol; }
        public long getStartTime() { return startTime; }
        public long getEndTime() { return endTime; }
        public BackupResult getResult() { return result; }
        
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public void setResult(BackupResult result) { this.result = result; }
        
        public long getDuration() {
            return endTime > 0 ? endTime - startTime : System.currentTimeMillis() - startTime;
        }
        
        public boolean isCompleted() {
            return result != null;
        }
        
        @Override
        public String toString() {
            return String.format("BackupActivity{protocol='%s', duration=%dms, status=%s}",
                protocol, getDuration(), result != null ? result.getStatus() : "IN_PROGRESS");
        }
    }
    
    /**
     * Comprehensive backup statistics
     */
    public static class BackupStats {
        private final int totalAttempted;
        private final int totalSuccessful;
        private final int totalFailed;
        private final int totalSkipped;
        private final double successRate;
        private final long averageTimeMs;
        private final long lastBackupTime;
        private final int recentActivities;
        
        public BackupStats(int totalAttempted, int totalSuccessful, int totalFailed, int totalSkipped,
                          double successRate, long averageTimeMs, long lastBackupTime, int recentActivities) {
            this.totalAttempted = totalAttempted;
            this.totalSuccessful = totalSuccessful;
            this.totalFailed = totalFailed;
            this.totalSkipped = totalSkipped;
            this.successRate = successRate;
            this.averageTimeMs = averageTimeMs;
            this.lastBackupTime = lastBackupTime;
            this.recentActivities = recentActivities;
        }
        
        // Getters
        public int getTotalAttempted() { return totalAttempted; }
        public int getTotalSuccessful() { return totalSuccessful; }
        public int getTotalFailed() { return totalFailed; }
        public int getTotalSkipped() { return totalSkipped; }
        public double getSuccessRate() { return successRate; }
        public long getAverageTimeMs() { return averageTimeMs; }
        public long getLastBackupTime() { return lastBackupTime; }
        public int getRecentActivities() { return recentActivities; }
        
        @Override
        public String toString() {
            return String.format("BackupStats{attempted=%d, successful=%d, failed=%d, skipped=%d, " +
                    "successRate=%.1f%%, avgTime=%dms, recentActivities=%d}",
                totalAttempted, totalSuccessful, totalFailed, totalSkipped, 
                successRate, averageTimeMs, recentActivities);
        }
    }
}
