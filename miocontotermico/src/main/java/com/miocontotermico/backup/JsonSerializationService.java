package com.miocontotermico.backup;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.miocontotermico.pojo.Procedure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;

/**
 * Service for JSON serialization of procedure data
 * 
 * <AUTHOR> Augster
 */
public class JsonSerializationService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(JsonSerializationService.class);
    
    private final ObjectMapper objectMapper;
    
    public JsonSerializationService() {
        this.objectMapper = new ObjectMapper();
        
        // Configure ObjectMapper for better JSON output
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * Serializes a procedure to JSON byte array
     * @param procedure the procedure to serialize
     * @return JSON data as byte array, or null on error
     */
    public byte[] serializeProcedure(Procedure procedure) {
        if (procedure == null) {
            LOGGER.error("Cannot serialize null procedure");
            return null;
        }
        
        try {
            String json = objectMapper.writeValueAsString(procedure);
            return json.getBytes(StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            LOGGER.error("Error serializing procedure {} to JSON: {}", 
                procedure.getProtocol(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Serializes a procedure to JSON string
     * @param procedure the procedure to serialize
     * @return JSON string, or null on error
     */
    public String serializeProcedureToString(Procedure procedure) {
        if (procedure == null) {
            LOGGER.error("Cannot serialize null procedure");
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(procedure);
            
        } catch (Exception e) {
            LOGGER.error("Error serializing procedure {} to JSON string: {}", 
                procedure.getProtocol(), e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Deserializes JSON to a procedure object
     * @param jsonData the JSON data
     * @return procedure object, or null on error
     */
    public Procedure deserializeProcedure(byte[] jsonData) {
        if (jsonData == null || jsonData.length == 0) {
            LOGGER.error("Cannot deserialize null or empty JSON data");
            return null;
        }
        
        try {
            String json = new String(jsonData, StandardCharsets.UTF_8);
            return objectMapper.readValue(json, Procedure.class);
            
        } catch (Exception e) {
            LOGGER.error("Error deserializing JSON to procedure: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Deserializes JSON string to a procedure object
     * @param json the JSON string
     * @return procedure object, or null on error
     */
    public Procedure deserializeProcedure(String json) {
        if (json == null || json.trim().isEmpty()) {
            LOGGER.error("Cannot deserialize null or empty JSON string");
            return null;
        }
        
        try {
            return objectMapper.readValue(json, Procedure.class);
            
        } catch (Exception e) {
            LOGGER.error("Error deserializing JSON string to procedure: {}", e.getMessage(), e);
            return null;
        }
    }
}
