package com.miocontotermico.backup;

import com.miocontotermico.dao.ProcedureDao;
import com.miocontotermico.pojo.Procedure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Utility class for testing the backup export system
 * 
 * <AUTHOR> Augster
 */
public class BackupExportTestUtility {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupExportTestUtility.class);
    
    /**
     * Tests the backup export system with a date range
     */
    public static void testBackupExport() {
        LOGGER.info("=== BACKUP EXPORT TEST ===");
        
        try {
            // Test date range (last 30 days)
            Date endDate = new Date();
            Date startDate = new Date(endDate.getTime() - (30L * 24 * 60 * 60 * 1000)); // 30 days ago
            
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            LOGGER.info("Testing export for date range: {} - {}", 
                dateFormat.format(startDate), dateFormat.format(endDate));
            
            // Load procedures in date range
            List<Procedure> procedures = ProcedureDao.loadProcedureListByDateRangeAndStatus(
                startDate, endDate, null, null, null);
            
            LOGGER.info("Found {} total procedures in date range", procedures.size());
            
            // Filter non-draft procedures
            int nonDraftCount = 0;
            int draftCount = 0;
            
            for (Procedure procedure : procedures) {
                if (procedure.getStatus() != null && !"draft".equalsIgnoreCase(procedure.getStatus())) {
                    nonDraftCount++;
                    LOGGER.debug("Non-draft procedure: {} (Status: {})", 
                        procedure.getProtocol(), procedure.getStatus());
                } else {
                    draftCount++;
                    LOGGER.debug("Draft procedure: {} (Status: {})", 
                        procedure.getProtocol(), procedure.getStatus());
                }
            }
            
            LOGGER.info("Procedures breakdown:");
            LOGGER.info("  - Non-draft (exportable): {}", nonDraftCount);
            LOGGER.info("  - Draft (skipped): {}", draftCount);
            LOGGER.info("  - Total: {}", procedures.size());
            
            if (nonDraftCount > 0) {
                LOGGER.info("✓ Export system ready - {} procedures would be exported", nonDraftCount);
                
                // Show first few procedures that would be exported
                int showCount = Math.min(5, nonDraftCount);
                LOGGER.info("First {} procedures to export:", showCount);
                
                int shown = 0;
                for (Procedure procedure : procedures) {
                    if (procedure.getStatus() != null && !"draft".equalsIgnoreCase(procedure.getStatus())) {
                        LOGGER.info("  - Protocol: {}, Status: {}, Date: {}", 
                            procedure.getProtocol(), 
                            procedure.getStatus(),
                            procedure.getDate() != null ? dateFormat.format(procedure.getDate()) : "N/A");
                        shown++;
                        if (shown >= showCount) break;
                    }
                }
            } else {
                LOGGER.warn("⚠ No exportable procedures found in the specified date range");
            }
            
            // Test backup trigger availability
            LOGGER.info("Testing backup trigger availability...");
            BackupTrigger backupTrigger = BackupTrigger.getInstance();
            if (backupTrigger != null) {
                LOGGER.info("✓ BackupTrigger instance available");
            } else {
                LOGGER.error("✗ BackupTrigger instance not available");
            }
            
            // Test background executor
            LOGGER.info("Testing background executor availability...");
            BackgroundTaskExecutor executor = BackgroundTaskExecutor.getInstance();
            if (executor != null) {
                LOGGER.info("✓ BackgroundTaskExecutor instance available");
            } else {
                LOGGER.error("✗ BackgroundTaskExecutor instance not available");
            }
            
            LOGGER.info("=== BACKUP EXPORT TEST COMPLETED ===");
            
        } catch (Exception e) {
            LOGGER.error("Error during backup export test: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Tests the backup export system with a specific date range
     */
    public static void testBackupExportWithDateRange(Date startDate, Date endDate) {
        LOGGER.info("=== BACKUP EXPORT TEST WITH CUSTOM DATE RANGE ===");
        
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            LOGGER.info("Testing export for date range: {} - {}", 
                dateFormat.format(startDate), dateFormat.format(endDate));
            
            // Load procedures in date range
            List<Procedure> procedures = ProcedureDao.loadProcedureListByDateRangeAndStatus(
                startDate, endDate, null, null, null);
            
            LOGGER.info("Found {} total procedures in date range", procedures.size());
            
            // Filter non-draft procedures
            int nonDraftCount = 0;
            for (Procedure procedure : procedures) {
                if (procedure.getStatus() != null && !"draft".equalsIgnoreCase(procedure.getStatus())) {
                    nonDraftCount++;
                }
            }
            
            LOGGER.info("Exportable procedures: {}", nonDraftCount);
            
            if (nonDraftCount > 0) {
                LOGGER.info("✓ Export would process {} procedures", nonDraftCount);
            } else {
                LOGGER.warn("⚠ No exportable procedures found in the specified date range");
            }
            
        } catch (Exception e) {
            LOGGER.error("Error during backup export test: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Simulates a backup export job (without actually performing backups)
     */
    public static void simulateBackupExport(Date startDate, Date endDate) {
        LOGGER.info("=== SIMULATING BACKUP EXPORT ===");
        
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            LOGGER.info("Simulating export for date range: {} - {}", 
                dateFormat.format(startDate), dateFormat.format(endDate));
            
            // Load procedures
            List<Procedure> procedures = ProcedureDao.loadProcedureListByDateRangeAndStatus(
                startDate, endDate, null, null, null);
            
            // Filter non-draft procedures
            int totalCount = 0;
            for (Procedure procedure : procedures) {
                if (procedure.getStatus() != null && !"draft".equalsIgnoreCase(procedure.getStatus())) {
                    totalCount++;
                }
            }
            
            LOGGER.info("Starting simulation for {} procedures", totalCount);
            
            // Simulate processing
            int processed = 0;
            int successful = 0;
            int failed = 0;
            
            for (Procedure procedure : procedures) {
                if (procedure.getStatus() != null && !"draft".equalsIgnoreCase(procedure.getStatus())) {
                    processed++;
                    
                    // Simulate processing time
                    try {
                        Thread.sleep(50); // 50ms per procedure
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                    
                    // Simulate success/failure (95% success rate)
                    if (Math.random() < 0.95) {
                        successful++;
                        LOGGER.debug("✓ Simulated backup for procedure: {}", procedure.getProtocol());
                    } else {
                        failed++;
                        LOGGER.debug("✗ Simulated failure for procedure: {}", procedure.getProtocol());
                    }
                    
                    // Log progress every 10 procedures
                    if (processed % 10 == 0 || processed == totalCount) {
                        int percentage = (processed * 100) / totalCount;
                        LOGGER.info("Progress: {}/{} ({}%) - {} successful, {} failed", 
                            processed, totalCount, percentage, successful, failed);
                    }
                }
            }
            
            LOGGER.info("=== SIMULATION COMPLETED ===");
            LOGGER.info("Total processed: {}", processed);
            LOGGER.info("Successful: {}", successful);
            LOGGER.info("Failed: {}", failed);
            LOGGER.info("Success rate: {}%", successful * 100 / Math.max(processed, 1));
            
        } catch (Exception e) {
            LOGGER.error("Error during backup export simulation: {}", e.getMessage(), e);
        }
    }
}
