package com.miocontotermico.backup;

import com.miocontotermico.pojo.Procedure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ThreadLocalRandom;

/**
 * Backup service with retry mechanisms and error handling
 * 
 * <AUTHOR> Augster
 */
public class RetryableBackupService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(RetryableBackupService.class);
    
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long BASE_DELAY_MS = 1000; // 1 second
    private static final long MAX_DELAY_MS = 30000; // 30 seconds
    private static final double JITTER_FACTOR = 0.1; // 10% jitter
    
    private final BackupService backupService;
    
    public RetryableBackupService() {
        this.backupService = new BackupService();
    }
    
    /**
     * Performs backup with retry logic
     * @param procedure the procedure to backup
     * @return backup result
     */
    public BackupResult backupProcedureWithRetry(Procedure procedure) {
        if (procedure == null) {
            return BackupResult.failure("Procedure cannot be null");
        }
        
        String protocol = procedure.getProtocol();
        BackupResult lastResult = null;
        
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                LOGGER.debug("Backup attempt {} of {} for procedure {}", attempt, MAX_RETRY_ATTEMPTS, protocol);
                
                BackupResult result = backupService.backupProcedure(procedure);
                
                if (result.isSuccess()) {
                    if (attempt > 1) {
                        LOGGER.info("Backup succeeded for procedure {} on attempt {}", protocol, attempt);
                    }
                    return result;
                }
                
                if (result.isSkipped()) {
                    // Don't retry skipped backups
                    return result;
                }
                
                lastResult = result;
                
                // Check if we should retry based on the error
                if (!shouldRetry(result, attempt)) {
                    LOGGER.warn("Not retrying backup for procedure {} after attempt {}: {}", 
                        protocol, attempt, result.getMessage());
                    return result;
                }
                
                // Wait before retry (except on last attempt)
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    long delay = calculateDelay(attempt);
                    LOGGER.info("Backup failed for procedure {} on attempt {}, retrying in {}ms: {}", 
                        protocol, attempt, delay, result.getMessage());
                    
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        return BackupResult.failure("Backup interrupted during retry delay");
                    }
                }
                
            } catch (Exception e) {
                lastResult = BackupResult.failure("Unexpected error: " + e.getMessage());
                LOGGER.error("Unexpected error in backup attempt {} for procedure {}: {}", 
                    attempt, protocol, e.getMessage(), e);
                
                // Wait before retry on unexpected errors (except on last attempt)
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    long delay = calculateDelay(attempt);
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return BackupResult.failure("Backup interrupted during retry delay");
                    }
                }
            }
        }
        
        LOGGER.error("Backup failed for procedure {} after {} attempts", protocol, MAX_RETRY_ATTEMPTS);
        return lastResult != null ? lastResult : BackupResult.failure("All retry attempts failed");
    }
    
    /**
     * Determines if a backup should be retried based on the result and attempt number
     */
    private boolean shouldRetry(BackupResult result, int attempt) {
        if (attempt >= MAX_RETRY_ATTEMPTS) {
            return false;
        }
        
        if (result.isSuccess() || result.isSkipped()) {
            return false;
        }
        
        String message = result.getMessage();
        if (message == null) {
            return true; // Retry unknown errors
        }
        
        String lowerMessage = message.toLowerCase();
        
        // Don't retry configuration errors
        if (lowerMessage.contains("configuration") || 
            lowerMessage.contains("invalid") ||
            lowerMessage.contains("not found") ||
            lowerMessage.contains("disabled")) {
            return false;
        }
        
        // Don't retry authentication errors
        if (lowerMessage.contains("unauthorized") ||
            lowerMessage.contains("authentication") ||
            lowerMessage.contains("access denied")) {
            return false;
        }
        
        // Retry network and temporary errors
        if (lowerMessage.contains("connection") ||
            lowerMessage.contains("timeout") ||
            lowerMessage.contains("network") ||
            lowerMessage.contains("temporary") ||
            lowerMessage.contains("rate limit") ||
            lowerMessage.contains("service unavailable")) {
            return true;
        }
        
        // Retry Dropbox API errors (they might be temporary)
        if (lowerMessage.contains("dropbox") ||
            lowerMessage.contains("api error")) {
            return true;
        }
        
        // Default: retry unknown errors
        return true;
    }
    
    /**
     * Calculates delay for exponential backoff with jitter
     */
    private long calculateDelay(int attempt) {
        // Exponential backoff: base_delay * 2^(attempt-1)
        long delay = BASE_DELAY_MS * (1L << (attempt - 1));
        
        // Cap at maximum delay
        delay = Math.min(delay, MAX_DELAY_MS);
        
        // Add jitter to avoid thundering herd
        double jitter = 1.0 + (ThreadLocalRandom.current().nextDouble() - 0.5) * 2 * JITTER_FACTOR;
        delay = (long) (delay * jitter);
        
        return Math.max(delay, BASE_DELAY_MS);
    }
    
    /**
     * Checks if a procedure should be backed up
     */
    public boolean shouldBackupProcedure(Procedure procedure) {
        return backupService.shouldBackupProcedure(procedure);
    }
}
