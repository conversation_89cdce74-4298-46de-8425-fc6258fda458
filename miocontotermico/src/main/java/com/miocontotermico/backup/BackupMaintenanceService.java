package com.miocontotermico.backup;

import com.miocontotermico.dao.FirmDao;
import com.miocontotermico.dropbox.DropboxConfig;
import com.miocontotermico.dropbox.DropboxService;
import com.miocontotermico.pojo.Firm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Maintenance utilities for backup system
 * 
 * <AUTHOR> Augster
 */
public class BackupMaintenanceService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BackupMaintenanceService.class);
    
    private static BackupMaintenanceService instance;
    
    private BackupMaintenanceService() {
        LOGGER.info("BackupMaintenanceService initialized");
    }
    
    /**
     * Gets the singleton instance
     */
    public static synchronized BackupMaintenanceService getInstance() {
        if (instance == null) {
            instance = new BackupMaintenanceService();
        }
        return instance;
    }
    
    /**
     * Performs comprehensive backup system maintenance
     * @return maintenance result
     */
    public MaintenanceResult performMaintenance() {
        LOGGER.info("Starting backup system maintenance...");
        
        MaintenanceResult result = new MaintenanceResult();
        
        try {
            // Clean up temporary files
            int tempFilesDeleted = cleanupTemporaryFiles();
            result.setTempFilesDeleted(tempFilesDeleted);
            
            // Test Dropbox connection
            boolean connectionOk = testDropboxConnection();
            result.setDropboxConnectionOk(connectionOk);
            
            // Clean up old backup activities
            cleanupOldBackupActivities();
            
            // Log current statistics
            logSystemStatistics();
            
            result.setSuccess(true);
            result.setMessage("Maintenance completed successfully");
            
            LOGGER.info("Backup system maintenance completed: {}", result);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("Maintenance failed: " + e.getMessage());
            LOGGER.error("Error during backup system maintenance: {}", e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * Cleans up temporary files created during backup operations
     */
    public int cleanupTemporaryFiles() {
        LOGGER.debug("Cleaning up temporary backup files...");
        
        AtomicInteger deletedCount = new AtomicInteger(0);
        
        try {
            // Get system temp directory
            String tempDir = System.getProperty("java.io.tmpdir");
            Path tempPath = Paths.get(tempDir);
            
            // Look for backup-related temporary files
            Files.walk(tempPath, 1)
                .filter(Files::isRegularFile)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    return fileName.startsWith("backup_") || 
                           fileName.startsWith("procedure_") ||
                           fileName.contains("_backup_temp_");
                })
                .filter(path -> {
                    try {
                        // Delete files older than 1 hour
                        long fileAge = System.currentTimeMillis() - Files.getLastModifiedTime(path).toMillis();
                        return fileAge > (60 * 60 * 1000); // 1 hour in milliseconds
                    } catch (IOException e) {
                        return false;
                    }
                })
                .forEach(path -> {
                    try {
                        Files.delete(path);
                        deletedCount.incrementAndGet();
                        LOGGER.debug("Deleted temporary file: {}", path);
                    } catch (IOException e) {
                        LOGGER.warn("Could not delete temporary file {}: {}", path, e.getMessage());
                    }
                });
                
        } catch (Exception e) {
            LOGGER.error("Error cleaning up temporary files: {}", e.getMessage(), e);
        }
        
        int deleted = deletedCount.get();
        if (deleted > 0) {
            LOGGER.info("Cleaned up {} temporary backup files", deleted);
        }
        
        return deleted;
    }
    
    /**
     * Tests Dropbox connection
     */
    public boolean testDropboxConnection() {
        try {
            Firm firm = FirmDao.loadFirm();
            if (firm == null) {
                LOGGER.warn("No firm configuration found for Dropbox connection test");
                return false;
            }
            
            DropboxConfig config = new DropboxConfig(firm);
            if (!config.isBackupEnabled()) {
                LOGGER.debug("Dropbox backup is disabled, skipping connection test");
                return true; // Not an error if disabled
            }
            
            DropboxService dropboxService = new DropboxService(config);
            return dropboxService.testConnection();
            
        } catch (Exception e) {
            LOGGER.error("Error testing Dropbox connection: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Cleans up old backup activities from monitor
     */
    public void cleanupOldBackupActivities() {
        try {
            BackupMonitor monitor = BackupMonitor.getInstance();
            // The monitor has its own cleanup logic that runs automatically
            LOGGER.debug("Backup activity cleanup completed");
        } catch (Exception e) {
            LOGGER.error("Error cleaning up backup activities: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Logs current system statistics
     */
    public void logSystemStatistics() {
        try {
            // Log backup statistics
            BackupMonitor.getInstance().logStats();
            
            // Log task queue statistics
            BackupTaskQueue.TaskQueueStats queueStats = BackupTaskQueue.getInstance().getStats();
            LOGGER.info("Current task queue stats: {}", queueStats);
            
            // Log executor statistics
            BackgroundTaskExecutor.ExecutorStats executorStats = BackgroundTaskExecutor.getInstance().getStats();
            LOGGER.info("Current executor stats: {}", executorStats);
            
        } catch (Exception e) {
            LOGGER.error("Error logging system statistics: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Lists all backup timestamps for a specific procedure protocol
     * @param protocol the procedure protocol
     * @return list of timestamp folder names, or empty list if none found
     */
    public List<String> listProcedureBackupTimestamps(String protocol) {
        List<String> timestamps = new ArrayList<>();

        try {
            Firm firm = FirmDao.loadFirm();
            if (firm == null) {
                LOGGER.warn("No firm configuration found");
                return timestamps;
            }

            DropboxConfig config = new DropboxConfig(firm);
            if (!config.isBackupEnabled()) {
                LOGGER.debug("Dropbox backup is disabled");
                return timestamps;
            }

            DropboxService dropboxService = new DropboxService(config);
            String basePath = config.getProcedureBasePath(protocol);

            // List all timestamp folders for this protocol
            com.dropbox.core.v2.files.ListFolderResult result = dropboxService.listFolder(basePath);
            if (result != null) {
                for (com.dropbox.core.v2.files.Metadata metadata : result.getEntries()) {
                    if (metadata instanceof com.dropbox.core.v2.files.FolderMetadata) {
                        timestamps.add(metadata.getName());
                    }
                }
            }

            // Sort timestamps (newest first)
            timestamps.sort((a, b) -> b.compareTo(a));

        } catch (Exception e) {
            LOGGER.error("Error listing backup timestamps for protocol {}: {}", protocol, e.getMessage(), e);
        }

        return timestamps;
    }

    /**
     * Shuts down the backup system gracefully
     */
    public void shutdown() {
        LOGGER.info("Shutting down backup system...");
        
        try {
            // Perform final maintenance
            performMaintenance();
            
            // Shutdown background executor
            BackgroundTaskExecutor.getInstance().shutdown();
            
            LOGGER.info("Backup system shutdown completed");
            
        } catch (Exception e) {
            LOGGER.error("Error during backup system shutdown: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Result of maintenance operation
     */
    public static class MaintenanceResult {
        private boolean success;
        private String message;
        private int tempFilesDeleted;
        private boolean dropboxConnectionOk;
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public int getTempFilesDeleted() { return tempFilesDeleted; }
        public void setTempFilesDeleted(int tempFilesDeleted) { this.tempFilesDeleted = tempFilesDeleted; }
        
        public boolean isDropboxConnectionOk() { return dropboxConnectionOk; }
        public void setDropboxConnectionOk(boolean dropboxConnectionOk) { this.dropboxConnectionOk = dropboxConnectionOk; }
        
        @Override
        public String toString() {
            return String.format("MaintenanceResult{success=%s, tempFilesDeleted=%d, dropboxOk=%s, message='%s'}",
                success, tempFilesDeleted, dropboxConnectionOk, message);
        }
    }
}
