package com.miocontotermico.dropbox;

import com.miocontotermico.pojo.Firm;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Dropbox configuration utility class
 * 
 * <AUTHOR> Augster
 */
public class DropboxConfig {
    
    private static final String DEFAULT_BACKUP_FOLDER = "Backup_MCT";
    
    private final String appKey;
    private final String appSecret;
    private final String accessToken;
    private final String refreshToken;
    private final boolean backupEnabled;
    private final String backupFolder;
    
    public DropboxConfig(Firm firm) {
        if (firm == null) {
            throw new IllegalArgumentException("Firm cannot be null");
        }
        
        this.appKey = firm.getDropboxAppKey();
        this.appSecret = firm.getDropboxAppSecret();
        this.accessToken = firm.getDropboxAccessToken();
        this.refreshToken = firm.getDropboxRefreshToken();
        this.backupEnabled = firm.getDropboxBackupEnabled() != null ? firm.getDropboxBackupEnabled() : false;
        this.backupFolder = StringUtils.isNotBlank(firm.getDropboxBackupFolder()) ? 
            firm.getDropboxBackupFolder() : DEFAULT_BACKUP_FOLDER;
    }
    
    /**
     * Validates if the Dropbox configuration is complete and valid
     * @return true if configuration is valid, false otherwise
     */
    public boolean isValid() {
        return StringUtils.isNotBlank(appKey) && 
               StringUtils.isNotBlank(appSecret) && 
               StringUtils.isNotBlank(accessToken);
    }
    
    /**
     * Validates if backup is enabled and configuration is valid
     * @return true if backup should be performed, false otherwise
     */
    public boolean isBackupEnabled() {
        return backupEnabled && isValid();
    }
    
    /**
     * Gets validation error message if configuration is invalid
     * @return error message or null if valid
     */
    public String getValidationError() {
        if (!backupEnabled) {
            return "Dropbox backup is disabled";
        }
        
        if (StringUtils.isBlank(appKey)) {
            return "Dropbox App Key is required";
        }
        
        if (StringUtils.isBlank(appSecret)) {
            return "Dropbox App Secret is required";
        }
        
        if (StringUtils.isBlank(accessToken)) {
            return "Dropbox Access Token is required";
        }
        
        return null;
    }
    
    // Getters
    public String getAppKey() {
        return appKey;
    }
    
    public String getAppSecret() {
        return appSecret;
    }
    
    public String getAccessToken() {
        return accessToken;
    }
    
    public String getRefreshToken() {
        return refreshToken;
    }
    
    public boolean isBackupEnabledFlag() {
        return backupEnabled;
    }
    
    public String getBackupFolder() {
        return backupFolder;
    }
    
    /**
     * Creates a backup folder path for a specific procedure protocol with timestamp
     * @param protocol the procedure protocol
     * @return the full backup folder path with timestamp subfolder
     */
    public String getProcedureBackupPath(String protocol) {
        if (StringUtils.isBlank(protocol)) {
            throw new IllegalArgumentException("Protocol cannot be blank");
        }

        // Create timestamp folder name (format: yyyy-MM-dd_HH-mm-ss)
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String timestamp = dateFormat.format(new Date());

        return "/" + backupFolder + "/" + protocol + "/" + timestamp;
    }

    /**
     * Creates the base backup folder path for a procedure protocol (without timestamp)
     * @param protocol the procedure protocol
     * @return the base backup folder path
     */
    public String getProcedureBasePath(String protocol) {
        if (StringUtils.isBlank(protocol)) {
            throw new IllegalArgumentException("Protocol cannot be blank");
        }
        return "/" + backupFolder + "/" + protocol;
    }

    /**
     * Validates the configuration
     * @return validation result
     */
    public ValidationResult validate() {
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // Critical errors that prevent backup functionality
        if (StringUtils.isBlank(appKey)) {
            errors.add("Dropbox App Key is required. Get it from your Dropbox App Console.");
        }

        if (StringUtils.isBlank(appSecret)) {
            errors.add("Dropbox App Secret is required. Get it from your Dropbox App Console.");
        }

        if (StringUtils.isBlank(accessToken)) {
            errors.add("Dropbox Access Token is required. Generate it in your Dropbox App Console or implement OAuth flow.");
        } else {
            // Validate access token format
            if (!accessToken.startsWith("sl.") && !accessToken.startsWith("oauth2:")) {
                warnings.add("Access token format may be incorrect. Dropbox tokens typically start with 'sl.' or 'oauth2:'");
            }
        }

        if (StringUtils.isBlank(backupFolder)) {
            errors.add("Backup folder name cannot be empty");
        } else {
            // Validate folder name
            if (backupFolder.contains("/") || backupFolder.contains("\\")) {
                errors.add("Backup folder name cannot contain path separators");
            }
            if (backupFolder.length() > 255) {
                errors.add("Backup folder name is too long (max 255 characters)");
            }
        }

        // Warnings for optional but recommended fields
        if (StringUtils.isBlank(refreshToken)) {
            warnings.add("Refresh token not configured. Consider implementing OAuth flow for production use.");
        }

        // Check if backup is enabled
        if (!backupEnabled) {
            warnings.add("Dropbox backup is currently disabled. Set dropboxBackupEnabled=true to enable.");
        }

        boolean isValid = errors.isEmpty();
        String message;
        if (isValid) {
            if (warnings.isEmpty()) {
                message = "Configuration is valid and complete";
            } else {
                message = "Configuration is valid with " + warnings.size() + " warning(s)";
            }
        } else {
            message = "Configuration has " + errors.size() + " error(s) that must be fixed";
        }

        return new ValidationResult(isValid, message, errors, warnings);
    }

    /**
     * Result of configuration validation
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;
        private final List<String> errors;
        private final List<String> warnings;

        public ValidationResult(boolean valid, String message, List<String> errors, List<String> warnings) {
            this.valid = valid;
            this.message = message;
            this.errors = errors != null ? new ArrayList<>(errors) : new ArrayList<>();
            this.warnings = warnings != null ? new ArrayList<>(warnings) : new ArrayList<>();
        }

        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
        public List<String> getErrors() { return new ArrayList<>(errors); }
        public List<String> getWarnings() { return new ArrayList<>(warnings); }

        public boolean hasErrors() { return !errors.isEmpty(); }
        public boolean hasWarnings() { return !warnings.isEmpty(); }

        public String getDetailedMessage() {
            StringBuilder sb = new StringBuilder(message);

            if (hasErrors()) {
                sb.append("\nErrors:");
                for (String error : errors) {
                    sb.append("\n  - ").append(error);
                }
            }

            if (hasWarnings()) {
                sb.append("\nWarnings:");
                for (String warning : warnings) {
                    sb.append("\n  - ").append(warning);
                }
            }

            return sb.toString();
        }

        @Override
        public String toString() {
            return String.format("ValidationResult{valid=%s, errors=%d, warnings=%d, message='%s'}",
                valid, errors.size(), warnings.size(), message);
        }
    }
}
