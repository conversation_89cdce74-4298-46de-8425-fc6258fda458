package com.miocontotermico.dropbox;

import com.dropbox.core.DbxException;
import com.dropbox.core.DbxRequestConfig;
import com.dropbox.core.v2.DbxClientV2;
import com.dropbox.core.v2.files.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Service for Dropbox API operations
 * 
 * <AUTHOR> Augster
 */
public class DropboxService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(DropboxService.class);
    private static final String CLIENT_IDENTIFIER = "MioContoTermico/1.0";
    
    private final DbxClientV2 client;
    private final DropboxConfig config;
    
    public DropboxService(DropboxConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("DropboxConfig cannot be null");
        }
        
        if (!config.isValid()) {
            throw new IllegalArgumentException("Invalid Dropbox configuration: " + config.getValidationError());
        }
        
        this.config = config;
        
        DbxRequestConfig requestConfig = DbxRequestConfig.newBuilder(CLIENT_IDENTIFIER).build();
        this.client = new DbxClientV2(requestConfig, config.getAccessToken());
    }
    
    /**
     * Creates a folder in Dropbox if it doesn't exist
     * @param folderPath the folder path to create
     * @return true if folder was created or already exists, false on error
     */
    public boolean createFolder(String folderPath) {
        if (StringUtils.isBlank(folderPath)) {
            LOGGER.error("Folder path cannot be blank");
            return false;
        }
        
        try {
            // Check if folder already exists
            if (folderExists(folderPath)) {
                LOGGER.debug("Folder already exists: {}", folderPath);
                return true;
            }
            
            // Create folder
            client.files().createFolderV2(folderPath);
            LOGGER.info("Created Dropbox folder: {}", folderPath);
            return true;
            
        } catch (CreateFolderErrorException e) {
            if (e.errorValue.isPath() && e.errorValue.getPathValue().isConflict()) {
                // Folder already exists
                LOGGER.debug("Folder already exists (conflict): {}", folderPath);
                return true;
            }
            LOGGER.error("Error creating folder {}: {}", folderPath, e.getMessage());
            return false;
        } catch (DbxException e) {
            LOGGER.error("Dropbox API error creating folder {}: {}", folderPath, e.getMessage());
            return false;
        }
    }
    
    /**
     * Checks if a folder exists in Dropbox
     * @param folderPath the folder path to check
     * @return true if folder exists, false otherwise
     */
    public boolean folderExists(String folderPath) {
        try {
            Metadata metadata = client.files().getMetadata(folderPath);
            return metadata instanceof FolderMetadata;
        } catch (DbxException e) {
            return false;
        }
    }
    
    /**
     * Uploads a file to Dropbox
     * @param filePath the destination path in Dropbox
     * @param fileContent the file content as byte array
     * @param originalFileName the original file name for logging
     * @return true if upload successful, false otherwise
     */
    public boolean uploadFile(String filePath, byte[] fileContent, String originalFileName) {
        if (StringUtils.isBlank(filePath)) {
            LOGGER.error("File path cannot be blank");
            return false;
        }
        
        if (fileContent == null || fileContent.length == 0) {
            LOGGER.error("File content cannot be null or empty");
            return false;
        }
        
        try (InputStream inputStream = new ByteArrayInputStream(fileContent)) {
            
            // Upload file with overwrite mode
            FileMetadata metadata = client.files().uploadBuilder(filePath)
                .withMode(WriteMode.OVERWRITE)
                .uploadAndFinish(inputStream);
                
            LOGGER.info("Uploaded file to Dropbox: {} (original: {}, size: {} bytes)", 
                filePath, originalFileName, fileContent.length);
            return true;
            
        } catch (UploadErrorException e) {
            LOGGER.error("Error uploading file {} (original: {}): {}", filePath, originalFileName, e.getMessage());
            return false;
        } catch (DbxException e) {
            LOGGER.error("Dropbox API error uploading file {} (original: {}): {}", filePath, originalFileName, e.getMessage());
            return false;
        } catch (IOException e) {
            LOGGER.error("IO error uploading file {} (original: {}): {}", filePath, originalFileName, e.getMessage());
            return false;
        }
    }
    
    /**
     * Deletes a file or folder from Dropbox
     * @param path the path to delete
     * @return true if deletion successful, false otherwise
     */
    public boolean delete(String path) {
        if (StringUtils.isBlank(path)) {
            LOGGER.error("Path cannot be blank");
            return false;
        }
        
        try {
            client.files().deleteV2(path);
            LOGGER.info("Deleted from Dropbox: {}", path);
            return true;
            
        } catch (DeleteErrorException e) {
            if (e.errorValue.isPathLookup() && 
                e.errorValue.getPathLookupValue().isNotFound()) {
                // File/folder doesn't exist, consider it successful
                LOGGER.debug("Path not found (already deleted): {}", path);
                return true;
            }
            LOGGER.error("Error deleting path {}: {}", path, e.getMessage());
            return false;
        } catch (DbxException e) {
            LOGGER.error("Dropbox API error deleting path {}: {}", path, e.getMessage());
            return false;
        }
    }
    
    /**
     * Lists files in a Dropbox folder
     * @param folderPath the folder path to list
     * @return list of file metadata, or null on error
     */
    public ListFolderResult listFolder(String folderPath) {
        try {
            return client.files().listFolder(folderPath);
        } catch (DbxException e) {
            LOGGER.error("Error listing folder {}: {}", folderPath, e.getMessage());
            return null;
        }
    }
    
    /**
     * Tests the Dropbox connection
     * @return true if connection is working, false otherwise
     */
    public boolean testConnection() {
        try {
            client.users().getCurrentAccount();
            LOGGER.info("Dropbox connection test successful");
            return true;
        } catch (DbxException e) {
            LOGGER.error("Dropbox connection test failed: {}", e.getMessage());
            return false;
        }
    }
}
