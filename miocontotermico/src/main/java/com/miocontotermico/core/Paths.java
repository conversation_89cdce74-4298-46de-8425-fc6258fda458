package com.miocontotermico.core;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Paths {

    private static final Logger LOGGER = LoggerFactory.getLogger(Paths.class.getName());

    ///////////
    // FRONTEND
    ///////////

    // main
    public static final String HOME                                = "/home";
    public static final String ABOUT                               = "/chi-siamo";
    public static final String CONTOTERMICO                        = "/contotermico";
    public static final String INVIAPRATICA                        = "/inviapratica";
    public static final String INVIAPRATICA_REGISTRATI             = "/inviapratica/registrati";
    public static final String CONTACTS                            = "/contatti";
    public static final String CONTACTS_SEND                       = "/contatti/invia";
    public static final String NEWS                                = "/news";
    public static final String NEWS_DETAIL                         = "/news/dettaglio";
    public static final String NEWS_DETAIL_IDENTIFIER              = "/news/dettaglio/:identifier";

    //////////
    // BACKEND
    //////////
    
    // profile
    public static final String PROFILE                             = "/profile";
    public static final String PROFILE_SAVE                        = "/profile/save";
    
    public static final String SMTP                                = "/smtp";
    public static final String SMTP_SAVE                           = "/smtp/save";

    // agency
    public static final String AGENCY                              = "/agency";
    public static final String AGENCY_SAVE                         = "/agency/save";

    // login
    public static final String LOGIN                               = "/login";
    public static final String LOGIN_DO                            = "/login/do";
    public static final String FORGOT                              = "/forgot";
    public static final String FORGOT_SEND                         = "/forgot/send";
    public static final String LOGOUT_DO                           = "/logout/do";

    // users
    public static final String USERS                               = "/users";    
    public static final String USER_DETAIL                         = "/user/detail";    
    public static final String USER_REMOVE                         = "/user/remove";
    public static final String USER_UPDATE_CREDIT                  = "/user/update/credit";
    public static final String USER_STATUS_UPDATE                  = "/user/status/update";
    
    // system users
    public static final String SYSTEM_USERS                        = "/system-users";    
    public static final String SYSTEM_USERS_ADD                    = "/system-users/add";
    public static final String SYSTEM_USERS_ADD_SAVE               = "/system-users/add/save";
    public static final String SYSTEM_USER_EDIT                    = "/system-user/edit";
    public static final String SYSTEM_USER_EDIT_SAVE               = "/system-user/edit/save";
    public static final String SYSTEM_USER_REMOVE                  = "/system-user/remove";
    
    // buy credit
    public static final String BUY_CREDIT                          = "/shop/credit";    
    public static final String BUY_CREDIT_SEND                     = "/shop/payment/send";
    
    // paid
    public static final String PAID                                = "/shop/paid";
    
    // procedures
    public static final String PROCEDURES                          = "/procedures";    
    
    // procedure
    public static final String PROCEDURES_ADD                      = "/procedures/add";
    public static final String PROCEDURES_ADD_INFO_SAVE            = "/procedures/add/info/save";
    public static final String PROCEDURES_ADD_SAVE                 = "/procedures/add/save";
    public static final String PROCEDURES_ADD_FILEID_REMOVE        = "/procedures/add/fileid/remove";
    
    public static final String PROCEDURE_EDIT                      = "/procedure/edit";
    public static final String PROCEDURE_EDIT_SAVE                 = "/procedure/edit/save";
    public static final String PROCEDURES_EDIT_FILEID_REMOVE       = "/procedures/edit/fileid/remove";
    public static final String PROCEDURE_REMOVE                    = "/procedure/remove";
    public static final String PROCEDURE_STATUS_UPDATE             = "/procedure/status/update";

    // procedure notes
    public static final String PROCEDURE_NOTES_LOAD                = "/procedure/notes/load";
    public static final String PROCEDURE_NOTE_CREATE               = "/procedure/note/create";
    public static final String PROCEDURE_NOTE_UPDATE               = "/procedure/note/update";
    public static final String PROCEDURE_NOTE_DELETE               = "/procedure/note/delete";
    
    // evaluations
    public static final String EVALUATIONS                          = "/evaluations";    
    
    // evaluations
    public static final String EVALUATIONS_ADD                      = "/evaluations/add";
    public static final String EVALUATIONS_ADD_INFO_SAVE            = "/evaluations/add/info/save";
    public static final String EVALUATIONS_ADD_SAVE                 = "/evaluations/add/save";
    public static final String EVALUATIONS_ADD_FILEID_REMOVE        = "/evaluations/add/fileid/remove";

    
    public static final String EVALUATION_EDIT                      = "/evaluation/edit";
    public static final String EVALUATION_EDIT_SAVE                 = "/evaluation/edit/save";
    public static final String EVALUATION_REMOVE                    = "/evaluation/remove";
    public static final String EVALUATION_STATUS_UPDATE             = "/evaluation/status/update";
    
    // eneaprocedures
    public static final String ENEAPROCEDURES                       = "/eneaprocedures";    
    
    // eneaprocedure
    public static final String ENEAPROCEDURES_ADD                   = "/eneaprocedures/add";
    public static final String ENEAPROCEDURES_ADD_INFO_SAVE         = "/eneaprocedures/add/info/save";
    public static final String ENEAPROCEDURES_ADD_SAVE              = "/eneaprocedures/add/save";
    public static final String ENEAPROCEDURES_ADD_FILEID_REMOVE     = "/eneaprocedures/add/fileid/remove";
    
    public static final String ENEAPROCEDURE_EDIT                   = "/eneaprocedure/edit";
    public static final String ENEAPROCEDURE_EDIT_SAVE              = "/eneaprocedure/edit/save";
    public static final String ENEAPROCEDURE_EDIT_FILEID_REMOVE     = "/eneaprocedure/edit/fileid/remove";
    public static final String ENEAPROCEDURE_REMOVE                 = "/eneaprocedure/remove";
    public static final String ENEAPROCEDURE_STATUS_UPDATE          = "/eneaprocedure/status/update";
    
    // invoicediscounts
    public static final String INVOICEDISCOUNTS                     = "/invoicediscounts";    
    
    // invoicediscount
    public static final String INVOICEDISCOUNTS_ADD                 = "/invoicediscounts/add";
    public static final String INVOICEDISCOUNTS_ADD_INFO_SAVE       = "/invoicediscounts/add/info/save";
    public static final String INVOICEDISCOUNTS_ADD_SAVE            = "/invoicediscounts/add/save";
    public static final String INVOICEDISCOUNTS_ADD_FILEID_REMOVE   = "/invoicediscounts/add/fileid/remove";
    
    public static final String INVOICEDISCOUNT_EDIT                 = "/invoicediscount/edit";
    public static final String INVOICEDISCOUNT_EDIT_SAVE            = "/invoicediscount/edit/save";
    public static final String INVOICEDISCOUNT_EDIT_FILEID_REMOVE   = "/invoicediscount/edit/fileid/remove";
    public static final String INVOICEDISCOUNT_REMOVE               = "/invoicediscount/remove";
    public static final String INVOICEDISCOUNT_STATUS_UPDATE        = "/invoicediscount/status/update";

    // backup export
    public static final String BACKUP_EXPORT                            = "/backup-export";
    public static final String BACKUP_EXPORT_START                      = "/backup-export/start";
    public static final String BACKUP_EXPORT_PROGRESS                   = "/backup-export/progress";

    // payments
    public static final String PAYMENTS                                  = "/payments";
    public static final String PAYMENTS_ADD                              = "/payments/add";
    public static final String PAYMENTS_ADD_SAVE                         = "/payments/add/save";
    public static final String PAYMENT_VIEW                              = "/payment/view";
    public static final String PAYMENT_VIEW_UPDATE                       = "/payment/view/update";
    public static final String PAYMENT_SHOPPABLE_UPDATE                  = "/payment/shoppable/update";
    public static final String PAYMENT_REMOVE                            = "/payment/remove";

    // payment platforms
    public static final String PAYMENT_PLATFORMS                         = "/paymentplatforms";
    public static final String PAYMENT_PLATFORMS_ADD                     = "/paymentplatforms/add";
    public static final String PAYMENT_PLATFORMS_ADD_SAVE                = "/paymentplatforms/add/save";
    public static final String PAYMENT_PLATFORM_VIEW                     = "/paymentplatform/view";
    public static final String PAYMENT_PLATFORM_VIEW_UPDATE              = "/paymentplatform/view/update";
    public static final String PAYMENT_PLATFORM_REMOVE                   = "/paymentplatform/remove";
    
    // orders
    public static final String ORDERS                                    = "/orders";
    public static final String ORDERS_DATA                               = "/orders/data";
    public static final String ORDERS_PRINT                              = "/orders/print";
    public static final String ORDER_VIEW                                = "/order/view";
    public static final String ORDER_PRINT                               = "/order/print";
    public static final String ORDER_STATUS_UPDATE                       = "/order/status/update";
    public static final String ORDER_PAYMENT_STATUS_UPDATE               = "/order/payment/status/update";
    
    // post
    public static final String POSTS                               = "/posts";       
    public static final String POST_EDIT                           = "/post/edit";
    public static final String POST_EDIT_SAVE                      = "/post/edit/save";
    public static final String POST_REMOVE                         = "/post/remove";

    // paperwork
    public static final String PAPERWORKS                          = "/paperworks";       
    public static final String PAPERWORKS_VIEW                     = "/paperworks/view";       
    public static final String PAPERWORK_EDIT                      = "/paperwork/edit";
    public static final String PAPERWORK_EDIT_SAVE                 = "/paperwork/edit/save";
    public static final String PAPERWORK_REMOVE                    = "/paperwork/remove";
    public static final String PAPERWORK_EDIT_FILEID_REMOVE        = "/paperwork/edit/fileid/remove";

    // mail
    public static final String MAILS                               = "/mails";
    public static final String MAIL_DETAIL                         = "/mail/edit";
    public static final String MAIL_DETAIL_SAVE                    = "/mail/edit/save";

    // statistics
    public static final String STATISTICS                          = "/statistics";
    
    // support
    public static final String IMAGE                               = "/image";
    public static final String THUMBNAIL                           = "/thumbnail";
    public static final String FILE                                = "/file";
    public static final String FILEZIP                             = "/filezip";
    
    // data
    public static final String DATA_CITIES                         = "/data/cities";
    public static final String DATA_PROCEDURES                     = "/data/immobili";
    public static final String DATA_STATISTICS_PROCEDURES_BY_USER  = "/data/statistics/procedures-by-user";

    ///////////
    // KEYWORDS
    public static final String SUCCESS_SUFFIX                      = "/success";
    public static final String ERROR_SUFFIX                        = "/error";


    /////////////////////
    // utility methods //
    private static Map<String, String> _paths;
    
    public static Map<String, String> paths() {
        if (_paths == null) {
            
            _paths = new HashMap<>();
            for (Field field : Paths.class.getFields()) {
                if (field.getType().isAssignableFrom(String.class)) {
                    
                    String name = field.getName();
                    String path = null;
                    try {
                        path = (String) field.get(null);
                    } catch (IllegalArgumentException | IllegalAccessException ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    
                    // put base value
                    _paths.put(name, path);
                    
                    // put annotated values
                    LocalizedPath[] annotations = field.getDeclaredAnnotationsByType(LocalizedPath.class);
                    if ((annotations != null) &&
                                (annotations.length > 0)) {
                        
                        // add other languages
                        for (LocalizedPath annotation : annotations) {
                            _paths.put(name + "_" + StringUtils.upperCase(annotation.language()), annotation.path());
                        }
                        
                        // this language intentionally differs from the one present
                        // in defaults class... path constants are in english, by design
                        final String language = "en";
                        _paths.put(name + "_" + StringUtils.upperCase(language), path);

                    }
                    
                }
            }
            
        }
        
        return _paths;
    }

    public static String[] localizedPaths(String path) {
        List<String> paths = null;

        if (StringUtils.isNotBlank(path)) {

            
            //////////////////////////////////////
            // base path (without language suffix)
            paths = new ArrayList<>();
            paths.add(path);
            
            
            /////////////////////////////////////////
            // localized paths (with language suffix)
            
            // match path name
            String name = null;
            for (String key : paths().keySet()) {
                if (StringUtils.equals(paths().get(key), path)) {
                    if (StringUtils.isBlank(name) || (key.length() < StringUtils.length(name))) {
                        name = key;
                    }
                }
            }
            
            // localized paths
            LocalizedPath[] annotations = null;
            if (StringUtils.isNotBlank(name)) {
                for (Field field : Paths.class.getFields()) {
                    if (field.getType().isAssignableFrom(String.class)) {
                        if (StringUtils.equals(name, field.getName())) {
                            LocalizedPath[] anns = field.getDeclaredAnnotationsByType(LocalizedPath.class);
                            if ((anns != null) &&
                                        (anns.length > 0)) {
                                annotations = anns;
                            }
                            break;
                        }
                    }
                }
            } else {
                LOGGER.error("path not found " + path);
            }
            
            if ((annotations != null) &&
                        (annotations.length > 0)) {

                // add other languages
                for (LocalizedPath annotation : annotations) {
                    paths.add(annotation.path());
                }

            }
            
        } else {
            LOGGER.error("empty path");
        }
        
        // returning list as array of string
        String[] result = null;
        if ((paths != null) && (!paths.isEmpty())) {
            result = paths.toArray(new String[paths.size()]);
        }
        
        return result;
    }
    
    public static String back(String backUrl) {
        return StringUtils.isNotBlank(backUrl) ? ("?backUrl=" + backUrl) : "";
    }

    public static String success(String path) {
        return path + SUCCESS_SUFFIX;
    }

    public static String error(String path) {
        return path + ERROR_SUFFIX;
    }

    public static boolean hasSuccess(Request evaluation) {
        // a route ending with "/success" means that a confirmation message should be shown
        return (evaluation != null) ? StringUtils.containsIgnoreCase(evaluation.pathInfo(), SUCCESS_SUFFIX) : false;
    }

    public static boolean hasError(Request evaluation) {
        // a route ending with "/error" means that an error message should be shown
        return (evaluation != null) ? StringUtils.containsIgnoreCase(evaluation.pathInfo(), ERROR_SUFFIX) : false;
    }

}
