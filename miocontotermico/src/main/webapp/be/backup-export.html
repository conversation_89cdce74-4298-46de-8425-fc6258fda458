{% extends "be/include/base.html" %}

{% set currentPage = 'BACKUP_EXPORT' %}

{% block extrahead %}
    <title>Esportazione Backup</title>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="{{ contextPath }}/be/js/pages/backup-export.js?{{ buildNumber }}"></script>
    
    <style>
        .backup-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        
        .backup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .backup-header h2 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .backup-header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .date-range-section {
            margin-bottom: 30px;
        }
        
        .date-range-section label {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 10px;
            display: block;
        }
        
        .progress-section {
            margin-top: 30px;
            display: none;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .progress-text {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .progress-stats {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .progress {
            height: 25px;
            border-radius: 12px;
            background-color: #ecf0f1;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(45deg, #3498db, #2980b9);
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 12px;
        }
        
        .btn-export {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            border: none;
            color: white;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        
        .btn-export:hover {
            background: linear-gradient(45deg, #229954, #27ae60);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }
        
        .btn-export:disabled {
            background: #bdc3c7;
            transform: none;
            box-shadow: none;
            cursor: not-allowed;
        }
        
        .alert-custom {
            border-radius: 6px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .alert-info-custom {
            background-color: #e8f4fd;
            color: #2980b9;
            border-left: 4px solid #3498db;
        }
        
        .alert-success-custom {
            background-color: #d5f4e6;
            color: #27ae60;
            border-left: 4px solid #2ecc71;
        }
        
        .alert-danger-custom {
            background-color: #fdf2f2;
            color: #e74c3c;
            border-left: 4px solid #e74c3c;
        }

        .alert-warning-custom {
            background-color: #fef9e7;
            color: #f39c12;
            border-left: 4px solid #f39c12;
        }
        
        .export-log {
            background-color: #2c3e50;
            color: #ecf0f1;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
            display: none;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success {
            color: #2ecc71;
        }
        
        .log-error {
            color: #e74c3c;
        }
        
        .log-info {
            color: #3498db;
        }

        .no-display {
            display: none;
        }
    </style>
{% endblock %}

{% block content %}

<!-- Hidden divs for JavaScript paths -->
<div id="startExportUrl" class="no-display">{{ paths('BACKUP_EXPORT_START') }}</div>
<div id="progressUrlTemplate" class="no-display">{{ paths('BACKUP_EXPORT_PROGRESS') }}</div>

<div class="page-container">
    <div class="page-content">
        <div class="content-wrapper">

            <!-- Content area -->
            <div class="content">
                
                <div class="row">
                    <div class="col-lg-8 col-lg-offset-2">
                        
                        <div class="backup-card">
                            <div class="backup-header">
                                <h2><i class="icon-cloud-upload"></i> Esportazione Massiva Backup</h2>
                                <p>Seleziona un range di date per esportare tutte le pratiche su Dropbox</p>
                            </div>
                            
                            <!-- Alert informativo -->
                            <div class="alert alert-custom alert-info-custom">
                                <i class="icon-info22"></i>
                                <strong>Informazioni:</strong> Verranno esportate solo le pratiche con stato diverso da "draft". 
                                Ogni pratica verrà salvata in una cartella separata con timestamp.
                            </div>
                            
                            <!-- Form di esportazione -->
                            <form id="exportForm">
                                <div class="date-range-section">
                                    <label for="dateRange">Seleziona Range di Date:</label>
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="icon-calendar22"></i></span>
                                        <input type="text" class="form-control" id="dateRange" name="dateRange" placeholder="Seleziona range di date..." readonly>
                                    </div>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" class="btn btn-export" id="btnExport">
                                        <i class="icon-cloud-upload"></i> Avvia Esportazione
                                    </button>
                                </div>
                            </form>
                            
                            <!-- Sezione Progress -->
                            <div class="progress-section" id="progressSection">
                                <div class="progress-info">
                                    <span class="progress-text">Esportazione in corso...</span>
                                    <span class="progress-stats" id="progressStats">0 / 0 pratiche</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" id="progressBar" style="width: 0%">0%</div>
                                </div>
                            </div>
                            
                            <!-- Alert risultati -->
                            <div id="resultAlert" style="display: none;"></div>
                            
                            <!-- Log esportazione -->
                            <div class="export-log" id="exportLog"></div>
                            
                        </div>
                        
                    </div>
                </div>
                
            </div>
            <!-- /content area -->

        </div>
    </div>
</div>
{% endblock %}
